import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![carry-out](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxODRINzEydi02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMzg0di02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIxNmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDY1NkgxODRWMjU2aDEyOHY0OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di00OGgyNTZ2NDhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNDhoMTI4djU4NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTcxMiAzMDRjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di00OEgzODR2NDhjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di00OEgxODR2NTg0aDY1NlYyNTZINzEydjQ4em0tMTcuNSAxMjguOEw0ODEuOSA3MjUuNWExNi4xIDE2LjEgMCAwMS0yNiAwbC0xMjYuNC0xNzRjLTMuOC01LjMgMC0xMi43IDYuNS0xMi43aDU1LjJjNS4yIDAgMTAgMi41IDEzIDYuNmw2NC43IDg5IDE1MC45LTIwNy44YzMtNC4xIDcuOS02LjYgMTMtNi42SDY4OGM2LjUgMCAxMC4zIDcuNCA2LjUgMTIuOHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTY4OCA0MjBoLTU1LjJjLTUuMSAwLTEwIDIuNS0xMyA2LjZMNDY4LjkgNjM0LjRsLTY0LjctODljLTMtNC4xLTcuOC02LjYtMTMtNi42SDMzNmMtNi41IDAtMTAuMyA3LjQtNi41IDEyLjdsMTI2LjQgMTc0YTE2LjEgMTYuMSAwIDAwMjYgMGwyMTIuNi0yOTIuN2MzLjgtNS40IDAtMTIuOC02LjUtMTIuOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
