import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![diff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC4yIDMwNi42TDYxMS4zIDcyLjljLTYtNS43LTEzLjktOC45LTIyLjItOC45SDI5NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGgyNzdsMjE5IDIxMC42VjgyNGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjMyOS42YzAtOC43LTMuNS0xNy05LjgtMjN6TTU1My40IDIwMS40Yy02LTYtMTQuMS05LjQtMjIuNi05LjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MDRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTEyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjM5Ny4zYzAtOC41LTMuNC0xNi42LTkuNC0yMi42TDU1My40IDIwMS40ek01NjggNzUzYzAgMy44LTMuNCA3LTcuNSA3aC0yMjVjLTQuMSAwLTcuNS0zLjItNy41LTd2LTQyYzAtMy44IDMuNC03IDcuNS03aDIyNWM0LjEgMCA3LjUgMy4yIDcuNSA3djQyem0wLTIyMGMwIDMuOC0zLjQgNy03LjUgN0g0NzZ2ODQuOWMwIDMuOS0zLjEgNy4xLTcgNy4xaC00MmMtMy44IDAtNy0zLjItNy03LjFWNTQwaC04NC41Yy00LjEgMC03LjUtMy4yLTcuNS03di00MmMwLTMuOSAzLjQtNyA3LjUtN0g0MjB2LTg0LjljMC0zLjkgMy4yLTcuMSA3LTcuMWg0MmMzLjkgMCA3IDMuMiA3IDcuMVY0ODRoODQuNWM0LjEgMCA3LjUgMy4xIDcuNSA3djQyeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
