{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 16807591979137911850, "deps": [[4381063397040571828, "webview2_com", false, 8020530905907141150], [7653476968652377684, "windows", false, 1900602856807682700], [8292277814562636972, "tauri_utils", false, 16255483686337415295], [8319709847752024821, "uuid", false, 6688255699529898215], [8391357152270261188, "wry", false, 13586587422869012018], [11693073011723388840, "raw_window_handle", false, 810186779276519523], [13208667028893622512, "rand", false, 13022999967511982451], [14162324460024849578, "tauri_runtime", false, 14407933444267013667], [16228250612241359704, "build_script_build", false, 10766013547209065286]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-d2b6aeadae67d469\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}