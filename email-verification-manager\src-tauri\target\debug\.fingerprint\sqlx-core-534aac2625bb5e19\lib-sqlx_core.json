{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 15657897354478470176, "path": 9537734116687662121, "deps": [[5103565458935487, "futures_io", false, 17120161654326270260], [40386456601120721, "percent_encoding", false, 4716183187666870099], [530211389790465181, "hex", false, 16134193509067797232], [788558663644978524, "crossbeam_queue", false, 3361613572943227033], [966925859616469517, "ahash", false, 15574183779833285240], [1162433738665300155, "crc", false, 11269440874698495367], [1464803193346256239, "event_listener", false, 17798548142908730619], [1811549171721445101, "futures_channel", false, 8035147461814441769], [3150220818285335163, "url", false, 6406480644215810995], [3405817021026194662, "hashlink", false, 15188431770449214127], [3646857438214563691, "futures_intrusive", false, 3476256848898093644], [3666196340704888985, "smallvec", false, 7721015296564415594], [3712811570531045576, "byteorder", false, 3237356455867690525], [3722963349756955755, "once_cell", false, 10119690688052558844], [5986029879202738730, "log", false, 2453399834231293545], [6493259146304816786, "indexmap", false, 9863487921890502931], [7620660491849607393, "futures_core", false, 7956960529905399841], [8008191657135824715, "thiserror", false, 12830748378722880387], [8319709847752024821, "uuid", false, 6688255699529898215], [8606274917505247608, "tracing", false, 15786480488678434657], [9689903380558560274, "serde", false, 12644071661126814469], [9857275760291862238, "sha2", false, 8509013771633457921], [9897246384292347999, "chrono", false, 17117145050121805016], [10629569228670356391, "futures_util", false, 13170970476968110228], [10862088793507253106, "sqlformat", false, 16071454032600993354], [11295624341523567602, "rustls", false, 5051207196878477508], [12170264697963848012, "either", false, 8150772257318924254], [15932120279885307830, "memchr", false, 6786945194398862742], [16066129441945555748, "bytes", false, 4004719702291560006], [16311359161338405624, "rustls_pemfile", false, 12290816144544439737], [16362055519698394275, "serde_json", false, 3264631440749833641], [16973251432615581304, "tokio_stream", false, 7231801520010121433], [17106256174509013259, "atoi", false, 2312373430686136496], [17531218394775549125, "tokio", false, 11527827444229834036], [17605717126308396068, "paste", false, 17994028612777642760], [17652733826348741533, "webpki_roots", false, 882512041424143192]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-534aac2625bb5e19\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}