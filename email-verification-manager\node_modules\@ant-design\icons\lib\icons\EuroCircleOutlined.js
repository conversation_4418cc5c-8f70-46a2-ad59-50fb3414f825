"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _EuroCircleOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/EuroCircleOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var EuroCircleOutlined = function EuroCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _EuroCircleOutlined.default
  }));
};

/**![euro-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptMTE3LjctNTg4LjZjLTE1LjktMy41LTM0LjQtNS40LTU1LjMtNS40LTEwNi43IDAtMTc4LjkgNTUuNy0xOTguNiAxNDkuOUgzNDRjLTQuNCAwLTggMy42LTggOHYyNy4yYzAgNC40IDMuNiA4IDggOGgyNi40Yy0uMyA0LjEtLjMgOC40LS4zIDEyLjh2MzYuOUgzNDRjLTQuNCAwLTggMy42LTggOFY1NjhjMCA0LjQgMy42IDggOCA4aDMwLjJjMTcuMiA5OS4yIDkwLjQgMTU4IDIwMC4yIDE1OCAyMC45IDAgMzkuNC0xLjcgNTUuMy01LjEgMy43LS44IDYuNC00IDYuNC03Ljh2LTQyLjhjMC01LTQuNi04LjgtOS41LTcuOC0xNC43IDIuOC0zMS45IDQuMS01MS44IDQuMS02OC41IDAtMTE0LjUtMzYuNi0xMjkuOC05OC42aDEzMC42YzQuNCAwIDgtMy42IDgtOHYtMjcuMmMwLTQuNC0zLjYtOC04LThINDM5LjJ2LTM2YzAtNC43IDAtOS40LjMtMTMuOGgxMzUuOWM0LjQgMCA4LTMuNiA4LTh2LTI3LjJjMC00LjQtMy42LTgtOC04SDQ0Ny4xYzE3LjItNTYuOSA2Mi4zLTkwLjQgMTI3LjYtOTAuNCAxOS45IDAgMzcuMSAxLjUgNTEuNyA0LjRhOCA4IDAgMDA5LjYtNy44di00Mi44YzAtMy44LTIuNi03LTYuMy03Ljh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(EuroCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'EuroCircleOutlined';
}
var _default = exports.default = RefIcon;