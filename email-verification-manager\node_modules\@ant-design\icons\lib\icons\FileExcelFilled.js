"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FileExcelFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FileExcelFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FileExcelFilled = function FileExcelFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FileExcelFilled.default
  }));
};

/**![file-excel](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTU3NS4zNCA0NzcuODRsLTYxLjIyIDEwMi4zTDQ1Mi4zIDQ3Ny44YTEyIDEyIDAgMDAtMTAuMjctNS43OWgtMzguNDRhMTIgMTIgMCAwMC02LjQgMS44NSAxMiAxMiAwIDAwLTMuNzUgMTYuNTZsODIuMzQgMTMwLjQyLTgzLjQ1IDEzMi43OGExMiAxMiAwIDAwLTEuODQgNi4zOSAxMiAxMiAwIDAwMTIgMTJoMzQuNDZhMTIgMTIgMCAwMDEwLjIxLTUuN2w2Mi43LTEwMS40NyA2Mi4zIDEwMS40NWExMiAxMiAwIDAwMTAuMjMgNS43MmgzNy40OGExMiAxMiAwIDAwNi40OC0xLjkgMTIgMTIgMCAwMDMuNjItMTYuNThsLTgzLjgzLTEzMC41NSA4NS4zLTEzMi40N2ExMiAxMiAwIDAwMS45LTYuNSAxMiAxMiAwIDAwLTEyLTEyaC0zNS43YTEyIDEyIDAgMDAtMTAuMjkgNS44NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileExcelFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileExcelFilled';
}
var _default = exports.default = RefIcon;