{"version": 3, "file": "isUnsafeAssignment.js", "sourceRoot": "", "sources": ["../src/isUnsafeAssignment.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AAGxC,6CAAgE;AAEhE;;;;;;;;;GASG;AACH,SAAgB,kBAAkB,CAChC,IAAa,EACb,QAAiB,EACjB,OAAuB,EACvB,UAAgC;IAEhC,OAAO,wBAAwB,CAC7B,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,UAAU,EACV,IAAI,GAAG,EAAE,CACV,CAAC;AACJ,CAAC;AAbD,gDAaC;AAED,SAAS,wBAAwB,CAC/B,IAAa,EACb,QAAiB,EACjB,OAAuB,EACvB,UAAgC,EAChC,OAAmC;IAEnC,IAAI,IAAA,0BAAa,EAAC,IAAI,CAAC,EAAE,CAAC;QACxB,uCAAuC;QACvC,IAAI,IAAA,8BAAiB,EAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,IAAA,0BAAa,EAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAED,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAE7C,IAAI,kBAAkB,EAAE,CAAC;QACvB,IAAI,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;QACvE,mDAAmD;QACnD,wDAAwD;QACxD;;;;;;;;;UASE;QAEF,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpC,mGAAmG;YACnG,+DAA+D;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IACE,UAAU,EAAE,IAAI,KAAK,sBAAc,CAAC,aAAa;YACjD,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;YACpD,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK;YAChC,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YACjC,UAAU,CAAC,aAAa,IAAI,IAAI,EAChC,CAAC;YACD,qCAAqC;YACrC,sFAAsF;YACtF,4FAA4F;YAC5F,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;QAC/C,MAAM,qBAAqB,GAAG,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjD,MAAM,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,WAAW,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,wBAAwB,CACrC,GAAG,EACH,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CACR,CAAC;YACF,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;YACpC,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}