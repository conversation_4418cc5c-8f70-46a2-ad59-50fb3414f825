import { invoke } from '@tauri-apps/api/tauri';
import type { 
  Account, 
  CreateAccountRequest, 
  UpdateAccountRequest, 
  Email, 
  EmailWithAccount 
} from './types';

// 账号管理API
export const accountApi = {
  // 创建账号
  async createAccount(account: CreateAccountRequest): Promise<Account> {
    return await invoke('create_account', { account });
  },

  // 获取所有账号
  async getAllAccounts(): Promise<Account[]> {
    return await invoke('get_all_accounts');
  },

  // 根据ID获取账号
  async getAccountById(id: number): Promise<Account> {
    return await invoke('get_account_by_id', { id });
  },

  // 更新账号
  async updateAccount(id: number, account: UpdateAccountRequest): Promise<Account> {
    return await invoke('update_account', { id, account });
  },

  // 删除账号
  async deleteAccount(id: number): Promise<void> {
    return await invoke('delete_account', { id });
  },

  // 测试IMAP连接
  async testConnection(account: CreateAccountRequest): Promise<boolean> {
    return await invoke('test_imap_connection', { account });
  },
};

// 邮件管理API
export const emailApi = {
  // 获取指定账号的最新邮件
  async fetchLatestEmails(accountId: number, count?: number): Promise<Email[]> {
    return await invoke('fetch_latest_emails', { accountId, count });
  },

  // 获取指定账号的邮件列表
  async getEmailsByAccount(accountId: number, limit?: number): Promise<Email[]> {
    return await invoke('get_emails_by_account', { accountId, limit });
  },

  // 刷新所有活跃账号的邮件
  async refreshAllAccounts(): Promise<EmailWithAccount[]> {
    return await invoke('refresh_all_accounts');
  },
};

// 通用错误处理
export const handleApiError = (error: unknown): string => {
  if (typeof error === 'string') {
    return error;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return '未知错误';
};
