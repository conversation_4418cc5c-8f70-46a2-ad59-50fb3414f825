{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 17667053810863203582, "deps": [[4450062412064442726, "dirs_next", false, 4754508734613253962], [4899080583175475170, "semver", false, 7200903675442271278], [7468248713591957673, "cargo_toml", false, 13860621184682100111], [8292277814562636972, "tauri_utils", false, 14122193339029557105], [9689903380558560274, "serde", false, 12644071661126814469], [10301936376833819828, "json_patch", false, 1128583256561629338], [13077543566650298139, "heck", false, 13910211334552500758], [13625485746686963219, "anyhow", false, 11331521509603507155], [14189313126492979171, "tauri_winres", false, 5133062795263063414], [15622660310229662834, "walkdir", false, 1473663514814095899], [16362055519698394275, "serde_json", false, 17541206816782419939]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-61870a80dd96bf2d\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}