import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![dislike](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4NS45IDQ5MC4zYzMuNi0xMiA1LjQtMjQuNCA1LjQtMzcgMC0yOC4zLTkuMy01NS41LTI2LjEtNzcuNyAzLjYtMTIgNS40LTI0LjQgNS40LTM3IDAtMjguMy05LjMtNTUuNS0yNi4xLTc3LjcgMy42LTEyIDUuNC0yNC40IDUuNC0zNyAwLTUxLjYtMzAuNy05OC4xLTc4LjMtMTE4LjRhNjYuMSA2Ni4xIDAgMDAtMjYuNS01LjRIMjczdjQyOGguM2w4NS44IDMxMC44QzM3Mi45IDg4OSA0MTguOSA5MjQgNDcwLjkgOTI0YzI5LjcgMCA1Ny40LTExLjggNzcuOS0zMy40IDIwLjUtMjEuNSAzMS00OS43IDI5LjUtNzkuNGwtNi0xMjIuOWgyMzkuOWMxMi4xIDAgMjMuOS0zLjIgMzQuMy05LjMgNDAuNC0yMy41IDY1LjUtNjYuMSA2NS41LTExMSAwLTI4LjMtOS4zLTU1LjUtMjYuMS03Ny43ek0xMTIgMTMydjM2NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NVYxMDBoLTY1Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
