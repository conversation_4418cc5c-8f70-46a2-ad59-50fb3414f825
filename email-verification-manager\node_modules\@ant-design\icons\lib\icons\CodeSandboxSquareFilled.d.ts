import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![code-sandbox-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMwNy45IDUzNi43bDg3LjYgNDkuOVY2ODFsOTYuNyA1NS45VjUyNC44TDMwNy45IDQxOC40ek04ODAgMTEySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTc1NS43IDY1My4yTDUxMiA3OTQgMjY4LjMgNjUzLjJWMzcxLjhsMTEwLTYzLjYtLjQtLjJoLjJMNTEyIDIzMWwxMzQgNzdoLS4ybC0uMy4yIDExMC4xIDYzLjZ2MjgxLjR6bS0yMjMuOSA4My43bDk3LjMtNTYuMnYtOTQuMWw4Ny00OS41VjQxOC41TDUzMS44IDUyNXptLTIwLTM1Mkw0MTggMzMxbC05MS4xIDUyLjYgMTg1LjIgMTA3IDE4NS4yLTEwNi45LTkxLjQtNTIuOHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
