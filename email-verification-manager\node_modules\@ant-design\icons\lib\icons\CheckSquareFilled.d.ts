import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![check-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjk1LjUgMzY1LjdsLTIxMC42IDI5MmEzMS44IDMxLjggMCAwMS01MS43IDBMMzA4LjUgNDg0LjljLTMuOC01LjMgMC0xMi43IDYuNS0xMi43aDQ2LjljMTAuMiAwIDE5LjkgNC45IDI1LjkgMTMuM2w3MS4yIDk4LjggMTU3LjItMjE4YzYtOC4zIDE1LjYtMTMuMyAyNS45LTEzLjNINjg5YzYuNSAwIDEwLjMgNy40IDYuNSAxMi43eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
