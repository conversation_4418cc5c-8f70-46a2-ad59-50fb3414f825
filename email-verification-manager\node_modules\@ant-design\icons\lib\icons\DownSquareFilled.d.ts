import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![down-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjk2LjUgNDEyLjdsLTE3OCAyNDZhNy45NSA3Ljk1IDAgMDEtMTIuOSAwbC0xNzgtMjQ2Yy0zLjgtNS4zIDAtMTIuNyA2LjUtMTIuN0gzODFjMTAuMiAwIDE5LjkgNC45IDI1LjkgMTMuMkw1MTIgNTU4LjZsMTA1LjItMTQ1LjRjNi04LjMgMTUuNi0xMy4yIDI1LjktMTMuMkg2OTBjNi41IDAgMTAuMyA3LjQgNi41IDEyLjd6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
