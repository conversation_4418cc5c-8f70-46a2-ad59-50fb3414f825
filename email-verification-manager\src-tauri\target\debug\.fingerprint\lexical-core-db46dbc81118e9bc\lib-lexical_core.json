{"rustc": 1842507548689473721, "features": "[\"arrayvec\", \"correct\", \"default\", \"ryu\", \"static_assertions\", \"std\", \"table\"]", "declared_features": "[\"arrayvec\", \"correct\", \"default\", \"dtoa\", \"format\", \"grisu3\", \"libm\", \"noinline\", \"property_tests\", \"proptest\", \"quickcheck\", \"radix\", \"rounding\", \"ryu\", \"static_assertions\", \"std\", \"table\", \"trim_floats\", \"unchecked_index\"]", "target": 15698117443813190224, "profile": 15657897354478470176, "path": 12543204549237462062, "deps": [[1216309103264968120, "ryu", false, 17120188247285221792], [2828590642173593838, "cfg_if", false, 4786576811675471617], [10435729446543529114, "bitflags", false, 2518700099420451862], [11279921689796057170, "arrayvec", false, 10248015020915891147], [13785866025199020095, "static_assertions", false, 11165093845000861362], [17462531364616038361, "build_script_build", false, 8804682104712116031]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lexical-core-db46dbc81118e9bc\\dep-lib-lexical_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}