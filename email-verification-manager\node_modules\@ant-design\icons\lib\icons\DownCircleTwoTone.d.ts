import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![down-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTE4NC40IDI3Ny43bC0xNzggMjQ2YTcuOTUgNy45NSAwIDAxLTEyLjkgMGwtMTc4LTI0NmMtMy44LTUuMyAwLTEyLjcgNi41LTEyLjdoNDYuOWMxMC4zIDAgMTkuOSA0LjkgMjUuOSAxMy4yTDUxMiA1NjMuNmwxMDUuMi0xNDUuNGM2LTguMyAxNS43LTEzLjIgMjUuOS0xMy4ySDY5MGM2LjUgMCAxMC4zIDcuNCA2LjQgMTIuN3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTY5MCA0MDVoLTQ2LjljLTEwLjIgMC0xOS45IDQuOS0yNS45IDEzLjJMNTEyIDU2My42IDQwNi44IDQxOC4yYy02LTguMy0xNS42LTEzLjItMjUuOS0xMy4ySDMzNGMtNi41IDAtMTAuMyA3LjQtNi41IDEyLjdsMTc4IDI0NmMzLjIgNC40IDkuNyA0LjQgMTIuOSAwbDE3OC0yNDZjMy45LTUuMy4xLTEyLjctNi40LTEyLjd6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
