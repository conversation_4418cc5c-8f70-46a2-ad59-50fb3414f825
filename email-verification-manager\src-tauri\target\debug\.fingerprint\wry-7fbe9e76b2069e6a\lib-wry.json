{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 16093708147335545770, "deps": [[3007252114546291461, "tao", false, 7692674283008661193], [3150220818285335163, "url", false, 6406480644215810995], [3540822385484940109, "windows_implement", false, 9332007579274842710], [3722963349756955755, "once_cell", false, 10119690688052558844], [4381063397040571828, "webview2_com", false, 8020530905907141150], [4405182208873388884, "http", false, 11575052487955420745], [4684437522915235464, "libc", false, 14148113365409035280], [5986029879202738730, "log", false, 2453399834231293545], [7653476968652377684, "windows", false, 1900602856807682700], [8008191657135824715, "thiserror", false, 12830748378722880387], [8391357152270261188, "build_script_build", false, 17416629419058030267], [9689903380558560274, "serde", false, 12644071661126814469], [11989259058781683633, "dunce", false, 1783921420998980814], [16362055519698394275, "serde_json", false, 3264631440749833641]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-7fbe9e76b2069e6a\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}