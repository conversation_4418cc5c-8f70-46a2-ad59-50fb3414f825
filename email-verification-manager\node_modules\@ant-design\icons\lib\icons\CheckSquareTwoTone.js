"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CheckSquareTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CheckSquareTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CheckSquareTwoTone = function CheckSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CheckSquareTwoTone.default
  }));
};

/**![check-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTMwLTM2Ny44aDQ2LjljMTAuMiAwIDE5LjkgNC45IDI1LjkgMTMuM2w3MS4yIDk4LjggMTU3LjItMjE4YzYtOC4zIDE1LjYtMTMuMyAyNS45LTEzLjNINjg4YzYuNSAwIDEwLjMgNy40IDYuNSAxMi43bC0yMTAuNiAyOTJhMzEuOCAzMS44IDAgMDEtNTEuNyAwTDMwNy41IDQ4NC45Yy0zLjgtNS4zIDAtMTIuNyA2LjUtMTIuN3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTQzMi4yIDY1Ny43YTMxLjggMzEuOCAwIDAwNTEuNyAwbDIxMC42LTI5MmMzLjgtNS4zIDAtMTIuNy02LjUtMTIuN2gtNDYuOWMtMTAuMyAwLTE5LjkgNS0yNS45IDEzLjNMNDU4IDU4NC4zbC03MS4yLTk4LjhjLTYtOC40LTE1LjctMTMuMy0yNS45LTEzLjNIMzE0Yy02LjUgMC0xMC4zIDcuNC02LjUgMTIuN2wxMjQuNyAxNzIuOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CheckSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CheckSquareTwoTone';
}
var _default = exports.default = RefIcon;