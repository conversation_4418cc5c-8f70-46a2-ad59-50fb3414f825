import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![google-plus](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3OS41IDQ3MC40Yy0uMy0yNy0uNC01NC4yLS41LTgxLjNoLTgwLjhjLS4zIDI3LS41IDU0LjEtLjcgODEuMy0yNy4yLjEtNTQuMi4zLTgxLjIuNnY4MC45YzI3IC4zIDU0LjIuNSA4MS4yLjguMyAyNyAuMyA1NC4xLjUgODEuMWg4MC45Yy4xLTI3IC4zLTU0LjEuNS04MS4zIDI3LjItLjMgNTQuMi0uNCA4MS4yLS43di04MC45Yy0yNi45LS4yLTU0LjEtLjItODEuMS0uNXptLTUzMCAuNGMtLjEgMzIuMyAwIDY0LjcuMSA5NyA1NC4yIDEuOCAxMDguNSAxIDE2Mi43IDEuOC0yMy45IDEyMC4zLTE4Ny40IDE1OS4zLTI3My45IDgwLjctODktNjguOS04NC44LTIyMCA3LjctMjg0IDY0LjctNTEuNiAxNTYuNi0zOC45IDIyMS4zIDUuOCAyNS40LTIzLjUgNDkuMi00OC43IDcyLjEtNzQuNy01My44LTQyLjktMTE5LjgtNzMuNS0xOTAtNzAuMy0xNDYuNi00LjktMjgxLjMgMTIzLjUtMjgzLjcgMjcwLjItOS40IDExOS45IDY5LjQgMjM3LjQgMTgwLjYgMjc5LjggMTEwLjggNDIuNyAyNTIuOSAxMy42IDMyMy43LTg2IDQ2LjctNjIuOSA1Ni44LTE0My45IDUxLjMtMjIwLTkwLjctLjctMTgxLjMtLjYtMjcxLjktLjN6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
