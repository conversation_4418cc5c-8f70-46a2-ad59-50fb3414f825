import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5OS42IDI3Ni41TDcwNSAzOTYuNCA1MTguNCAxNDcuNWE4LjA2IDguMDYgMCAwMC0xMi45IDBMMzE5IDM5Ni40IDEyNC4zIDI3Ni41Yy01LjctMy41LTEzLjEgMS4yLTEyLjIgNy45TDE4OC41IDg2NWMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuOC02LjctNi41LTExLjQtMTIuMy03Ljl6TTUxMiA3MzQuMmMtNjIuMSAwLTExMi42LTUwLjUtMTEyLjYtMTEyLjZTNDQ5LjkgNTA5IDUxMiA1MDlzMTEyLjYgNTAuNSAxMTIuNiAxMTIuNlM1NzQuMSA3MzQuMiA1MTIgNzM0LjJ6bTAtMTYwLjljLTI2LjYgMC00OC4yIDIxLjYtNDguMiA0OC4zIDAgMjYuNiAyMS42IDQ4LjMgNDguMiA0OC4zczQ4LjItMjEuNiA0OC4yLTQ4LjNjMC0yNi42LTIxLjYtNDguMy00OC4yLTQ4LjN6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
