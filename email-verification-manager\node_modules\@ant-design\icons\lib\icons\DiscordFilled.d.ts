import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![discord](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNODExLjE1IDg3YzUxLjE2IDAgOTIuNDEgNDEuMzYgOTQuODUgOTAuMDNWOTYwbC05Ny40LTgyLjY4LTUzLjQ4LTQ4LjY3LTU4LjM1LTUwLjg1IDI0LjM3IDgwLjJIMjEwLjQxYy01MSAwLTkyLjQxLTM4Ljc0LTkyLjQxLTkwLjA2VjE3Ny4yMWMwLTQ4LjY3IDQxLjQ4LTkwLjEgOTIuNi05MC4xaDYwMC4zek01ODguMTYgMjk0LjFoLTEuMDlsLTcuMzQgNy4yOGM3NS4zOCAyMS44IDExMS44NSA1NS44NiAxMTEuODUgNTUuODYtNDguNTgtMjQuMjgtOTIuMzYtMzYuNDItMTM2LjE0LTQxLjMyLTMxLjY0LTQuOTEtNjMuMjgtMi4zMy05MCAwaC03LjI4Yy0xNy4wOSAwLTUzLjQ1IDcuMjctMTAyLjE4IDI2LjctMTYuOTggNy4zOS0yNi43MiAxMi4yMi0yNi43MiAxMi4yMnMzNi40My0zNi40MiAxMTYuNzItNTUuODZsLTQuOS00LjlzLTYwLjgtMi4zMy0xMjYuNDQgNDYuMTVjMCAwLTY1LjY0IDExNC4yNi02NS42NCAyNTUuMTMgMCAwIDM2LjM2IDYzLjI0IDEzNi4xMSA2NS42NCAwIDAgMTQuNTUtMTkuMzcgMjkuMjctMzYuNDItNTYtMTctNzcuODItNTEuMDItNzcuODItNTEuMDJzNC44OCAyLjQgMTIuMTkgNy4yN2gyLjE4YzEuMDkgMCAxLjYuNTQgMi4xOCAxLjA5di4yMWMuNTguNTkgMS4wOSAxLjEgMi4xOCAxLjEgMTIgNC45NCAyNCA5LjggMzMuODIgMTQuNTNhMjk3LjU4IDI5Ny41OCAwIDAwNjUuNDUgMTkuNDhjMzMuODIgNC45IDcyLjU5IDcuMjcgMTE2LjczIDAgMjEuODItNC45IDQzLjY0LTkuNyA2NS40Ni0xOS40NCAxNC4xOC03LjI3IDMxLjYzLTE0LjU0IDUwLjgtMjYuNzkgMCAwLTIxLjgyIDM0LjAyLTgwLjE5IDUxLjAzIDEyIDE2Ljk0IDI4LjkxIDM2LjM0IDI4LjkxIDM2LjM0IDk5Ljc5LTIuMTggMTM4LjU1LTY1LjQyIDE0MC43My02Mi43MyAwLTE0MC42NS02Ni0yNTUuMTMtNjYtMjU1LjEzLTU5LjQ1LTQ0LjEyLTExNS4wOS00NS44LTEyNC45MS00NS44bDIuMDQtLjcyek01OTUgNDU0YzI1LjQ2IDAgNDYgMjEuNzYgNDYgNDguNDEgMCAyNi44My0yMC42NSA0OC41OS00NiA0OC41OXMtNDYtMjEuNzYtNDYtNDguMzdjLjA3LTI2Ljg0IDIwLjc1LTQ4LjUyIDQ2LTQ4LjUyem0tMTY1Ljg1IDBjMjUuMzUgMCA0NS44NSAyMS43NiA0NS44NSA0OC40MSAwIDI2LjgzLTIwLjY1IDQ4LjU5LTQ2IDQ4LjU5cy00Ni0yMS43Ni00Ni00OC4zN2MwLTI2Ljg0IDIwLjY1LTQ4LjUyIDQ2LTQ4LjUyeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
