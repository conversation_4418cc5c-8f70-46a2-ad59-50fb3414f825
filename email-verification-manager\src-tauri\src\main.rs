// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod models;
mod services;
mod database;

use commands::{account::*, email::*};
use database::init_database;
use services::CryptoService;

#[tokio::main]
async fn main() {
    // Initialize crypto service
    if let Err(e) = CryptoService::init() {
        eprintln!("Failed to initialize crypto service: {}", e);
        std::process::exit(1);
    }

    // Initialize database
    if let Err(e) = init_database().await {
        eprintln!("Failed to initialize database: {}", e);
        std::process::exit(1);
    }

    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            // Account management commands
            create_account,
            get_all_accounts,
            get_account_by_id,
            update_account,
            delete_account,
            test_imap_connection,
            // Email management commands
            fetch_latest_emails,
            get_emails_by_account,
            refresh_all_accounts
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
