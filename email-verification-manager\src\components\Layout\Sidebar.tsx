import React from 'react';
import { Layout, Menu } from 'antd';
import { UserOutlined, MailOutlined } from '@ant-design/icons';

const { Sider } = Layout;

type PageType = 'accounts' | 'codes';

interface SidebarProps {
  currentPage: PageType;
  onPageChange: (page: PageType) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ currentPage, onPageChange }) => {
  const menuItems = [
    {
      key: 'accounts',
      icon: <UserOutlined />,
      label: '账号管理',
      onClick: () => onPageChange('accounts'),
    },
    {
      key: 'codes',
      icon: <MailOutlined />,
      label: '验证码管理',
      onClick: () => onPageChange('codes'),
    },
  ];

  return (
    <Sider width={200} theme="dark">
      <div style={{ 
        height: 64, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        color: 'white',
        fontSize: '16px',
        fontWeight: 'bold',
        borderBottom: '1px solid #303030'
      }}>
        邮箱验证码管理
      </div>
      <Menu
        mode="inline"
        selectedKeys={[currentPage]}
        style={{ height: 'calc(100% - 64px)', borderRight: 0 }}
        theme="dark"
        items={menuItems}
      />
    </Sider>
  );
};

export default Sidebar;
