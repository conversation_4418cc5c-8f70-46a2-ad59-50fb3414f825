import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![interaction](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzI2IDU4NS43YzAgNTUuMy00NC43IDEwMC4xLTk5LjcgMTAwLjFINDIwLjZ2NTMuNGMwIDUuNy02LjUgOC44LTEwLjkgNS4zbC0xMDkuMS04NS43Yy0zLjUtMi43LTMuNS04IDAtMTAuN2wxMDkuMS04NS43YzQuNC0zLjUgMTAuOS0uMyAxMC45IDUuM3Y1My40aDIwNS43YzE5LjYgMCAzNS41LTE2IDM1LjUtMzUuNnYtNzguOWMwLTMuNyAzLTYuOCA2LjgtNi44aDUwLjdjMy43IDAgNi44IDMgNi44IDYuOHY3OS4xem0tMi42LTIwOS45bC0xMDkuMSA4NS43Yy00LjQgMy41LTEwLjkuMy0xMC45LTUuM3YtNTMuNEgzOTcuN2MtMTkuNiAwLTM1LjUgMTYtMzUuNSAzNS42djc4LjljMCAzLjctMyA2LjgtNi44IDYuOGgtNTAuN2MtMy43IDAtNi44LTMtNi44LTYuOHYtNzguOWMwLTU1LjMgNDQuNy0xMDAuMSA5OS43LTEwMC4xaDIwNS43di01My40YzAtNS43IDYuNS04LjggMTAuOS01LjNsMTA5LjEgODUuN2MzLjYgMi41IDMuNiA3LjguMSAxMC41eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
