use crate::models::{Account, CreateAccountRequest, UpdateAccountRequest};
use crate::services::{AccountService, ImapService};
use tauri::command;

#[command]
pub async fn create_account(account: CreateAccountRequest) -> Result<Account, String> {
    AccountService::create_account(account)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_all_accounts() -> Result<Vec<Account>, String> {
    AccountService::get_all_accounts()
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_account_by_id(id: i64) -> Result<Account, String> {
    AccountService::get_account_by_id(id)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn update_account(id: i64, account: UpdateAccountRequest) -> Result<Account, String> {
    AccountService::update_account(id, account)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn delete_account(id: i64) -> Result<(), String> {
    AccountService::delete_account(id)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn test_imap_connection(account: CreateAccountRequest) -> Result<bool, String> {
    // 创建临时账号对象用于测试
    let temp_account = Account {
        id: 0,
        name: account.name,
        email: account.email,
        imap_server: account.imap_server,
        imap_port: account.imap_port,
        username: account.username,
        password: account.password, // 这里直接使用明文密码进行测试
        use_tls: account.use_tls,
        created_at: String::new(),
        updated_at: String::new(),
        is_active: account.is_active,
    };
    
    // 为测试连接创建特殊的测试方法
    test_connection_with_plain_password(&temp_account)
        .await
        .map_err(|e| e.to_string())
}

async fn test_connection_with_plain_password(account: &Account) -> Result<bool, Box<dyn std::error::Error>> {
    use imap::Session;
    use native_tls::{TlsConnector, TlsStream};
    use std::net::TcpStream;
    
    let tls = TlsConnector::builder().build()?;
    
    let client = if account.use_tls {
        imap::connect((account.imap_server.as_str(), account.imap_port as u16), &account.imap_server, &tls)?
    } else {
        return Err("Non-TLS connections not supported".into());
    };

    match client.login(&account.username, &account.password) {
        Ok(mut session) => {
            session.logout().ok();
            Ok(true)
        }
        Err(_) => Ok(false),
    }
}
