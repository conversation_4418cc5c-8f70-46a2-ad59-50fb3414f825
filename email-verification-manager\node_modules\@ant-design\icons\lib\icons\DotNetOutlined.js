"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DotNetOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DotNetOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DotNetOutlined = function DotNetOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DotNetOutlined.default
  }));
};

/**![dot-net](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsLW9wYWNpdHk9Ii44OCI+PHBhdGggZD0iTTEwMS4yOCA2NjJjLTEwLjY1IDAtMTkuNTMtMy4zLTI2LjYzLTkuODktNy4xLTYuNi0xMC42NS0xNC43LTEwLjY1LTI0LjMyIDAtOS44OSAzLjY1LTE4IDEwLjk2LTI0LjMxIDcuMy02LjMyIDE2LjQyLTkuNDggMjcuMzUtOS40OCAxMS4wNiAwIDIwLjEgMy4yIDI3LjE0IDkuNTggNy4wMyA2LjM5IDEwLjU1IDE0LjQ2IDEwLjU1IDI0LjIxIDAgMTAuMDMtMy41OCAxOC4yNC0xMC43NiAyNC42My03LjE3IDYuMzktMTYuNDkgOS41OC0yNy45NiA5LjU4TTQ1OCA2NTdoLTY2Ljk3bC0xMjEuNC0xODUuMzVjLTcuMTMtMTAuODQtMTIuMDYtMTktMTQuOC0yNC40OGgtLjgyYzEuMSAxMC40MiAxLjY1IDI2LjMzIDEuNjUgNDcuNzJWNjU3SDE5M1YzNjJoNzEuNDlsMTE2Ljg5IDE3OS42YTQyMy4yMyA0MjMuMjMgMCAwMTE0Ljc5IDI0LjA2aC44MmMtMS4xLTYuODYtMS42NC0yMC4zNy0xLjY0LTQwLjUzVjM2Mkg0NTh6TTcwMiA2NTdINTI1VjM2MmgxNzAuMnY1NC4xSDU5MS40OXY2NS42M0g2ODh2NTMuOWgtOTYuNTJ2NjcuNDdINzAyek05NjAgNDE2LjFoLTgzLjk1VjY1N2gtNjYuNVY0MTYuMUg3MjZWMzYyaDIzNHoiIC8+PC9nPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(DotNetOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DotNetOutlined';
}
var _default = exports.default = RefIcon;