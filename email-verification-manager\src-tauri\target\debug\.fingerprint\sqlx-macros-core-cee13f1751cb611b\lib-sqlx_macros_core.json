{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 1032364163171458086, "deps": [[530211389790465181, "hex", false, 16134193509067797232], [996810380461694889, "sqlx_core", false, 8079823656883154559], [1441306149310335789, "tempfile", false, 2241485164868864586], [2713742371683562785, "syn", false, 7111228983072178131], [3060637413840920116, "proc_macro2", false, 3511945678907558886], [3150220818285335163, "url", false, 6406480644215810995], [3405707034081185165, "dotenvy", false, 3713289876987039522], [3722963349756955755, "once_cell", false, 10119690688052558844], [8045585743974080694, "heck", false, 12284683041956107731], [9689903380558560274, "serde", false, 12644071661126814469], [9857275760291862238, "sha2", false, 4054353303462043538], [11838249260056359578, "sqlx_sqlite", false, 7487095711745282445], [12170264697963848012, "either", false, 8150772257318924254], [16362055519698394275, "serde_json", false, 17541206816782419939], [17531218394775549125, "tokio", false, 17103909524077865931], [17990358020177143287, "quote", false, 12892848900342196043]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-cee13f1751cb611b\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}