import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![control](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
