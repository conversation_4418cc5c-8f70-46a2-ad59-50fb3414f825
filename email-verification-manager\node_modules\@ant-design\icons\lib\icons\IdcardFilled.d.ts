import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![idcard](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM3MyA0MTFjLTI4LjUgMC01MS43IDIzLjMtNTEuNyA1MnMyMy4yIDUyIDUxLjcgNTIgNTEuNy0yMy4zIDUxLjctNTItMjMuMi01Mi01MS43LTUyem01NTUtMjUxSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjA4IDQyMGMwLTQuNCAxLTggMi4zLThoMTIzLjRjMS4zIDAgMi4zIDMuNiAyLjMgOHY0OGMwIDQuNC0xIDgtMi4zIDhINjEwLjNjLTEuMyAwLTIuMy0zLjYtMi4zLTh2LTQ4em0tODYgMjUzaC00My45Yy00LjIgMC03LjYtMy4zLTcuOS03LjUtMy44LTUwLjUtNDYtOTAuNS05Ny4yLTkwLjVzLTkzLjQgNDAtOTcuMiA5MC41Yy0uMyA0LjItMy43IDcuNS03LjkgNy41SDIyNGE4IDggMCAwMS04LTguNGMyLjgtNTMuMyAzMi05OS43IDc0LjYtMTI2LjFhMTExLjggMTExLjggMCAwMS0yOS4xLTc1LjVjMC02MS45IDQ5LjktMTEyIDExMS40LTExMnMxMTEuNCA1MC4xIDExMS40IDExMmMwIDI5LjEtMTEgNTUuNS0yOS4xIDc1LjUgNDIuNyAyNi41IDcxLjggNzIuOCA3NC42IDEyNi4xLjQgNC42LTMuMiA4LjQtNy44IDguNHptMjc4LjktNTNINjE1LjFjLTMuOSAwLTcuMS0zLjYtNy4xLTh2LTQ4YzAtNC40IDMuMi04IDcuMS04aDE4NS43YzMuOSAwIDcuMSAzLjYgNy4xIDh2NDhoLjFjMCA0LjQtMy4yIDgtNy4xIDh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
