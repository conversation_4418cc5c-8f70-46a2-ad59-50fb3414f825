import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![ie-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzY1LjkgNTU2LjlINDM3LjFjMCAxMDAuNCAxNDQuMyAxMzYgMTk2LjggNDcuNGgxMjAuOGMtMzIuNiA5MS43LTExOS43IDE0Ni0yMTYuOCAxNDYtMzUuMSAwLTcwLjMtLjEtMTAxLjctMTUuNi04Ny40IDQ0LjUtMTgwLjMgNTYuNi0xODAuMy00MiAwLTQ1LjggMjMuMi0xMDcuMSA0NC0xNDVDMzM1IDQ4NCAzODEuMyA0MjIuOCA0MzUuNiAzNzQuNWMtNDMuNyAxOC45LTkxLjEgNjYuMy0xMjIgMTAxLjIgMjUuOS0xMTIuOCAxMjkuNS0xOTMuNiAyMzcuMS0xODYuNSAxMzAtNTkuOCAyMDkuNy0zNC4xIDIwOS43IDM4LjYgMCAyNy40LTEwLjYgNjMuMy0yMS40IDg3LjkgMjUuMiA0NS41IDMzLjMgOTcuNiAyNi45IDE0MS4yem0tNzIuMy0yNzIuNWMtMjQgMC01MS4xIDExLjctNzIuNiAyMiA0Ni4zIDE4IDg2IDU3LjMgMTEyLjMgOTkuNiA3LjEtMTguOSAxNC42LTQ3LjkgMTQuNi02Ny45IDAtMzItMjIuOC01My43LTU0LjMtNTMuN3pNNTQwLjUgMzk5LjFjLTUzLjcgMC0xMDIgMzkuNy0xMDQgOTQuOWgyMDhjLTItNTUuMS01MC42LTk0LjktMTA0LTk0Ljl6TTMyMC42IDYwMi45Yy03MyAxNTIuNCAxMS41IDE3Mi4yIDEwMC4zIDEyMy4zLTQ2LjYtMjcuNS04Mi42LTcyLjItMTAwLjMtMTIzLjN6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
