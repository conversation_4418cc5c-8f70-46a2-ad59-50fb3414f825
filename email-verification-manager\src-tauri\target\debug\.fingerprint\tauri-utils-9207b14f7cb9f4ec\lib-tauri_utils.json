{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 5990763849186779865, "deps": [[561782849581144631, "html5ever", false, 12688027876611175114], [3150220818285335163, "url", false, 6406480644215810995], [3334271191048661305, "windows_version", false, 14752512239702230232], [4071963112282141418, "serde_with", false, 9620876622289845231], [4899080583175475170, "semver", false, 5780744125036643049], [5986029879202738730, "log", false, 2453399834231293545], [6262254372177975231, "kuchiki", false, 4954276931684827912], [6606131838865521726, "ctor", false, 460528677461087658], [6997837210367702832, "infer", false, 10168897065111144013], [8008191657135824715, "thiserror", false, 12830748378722880387], [9689903380558560274, "serde", false, 12644071661126814469], [10301936376833819828, "json_patch", false, 2145278873056523419], [11989259058781683633, "dunce", false, 1783921420998980814], [14132538657330703225, "brotli", false, 11841727477002726077], [15622660310229662834, "walkdir", false, 8185383562088752505], [15932120279885307830, "memchr", false, 6786945194398862742], [16362055519698394275, "serde_json", false, 3264631440749833641], [17155886227862585100, "glob", false, 9366574394984496925], [17186037756130803222, "phf", false, 15198320271295849870]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-9207b14f7cb9f4ec\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}