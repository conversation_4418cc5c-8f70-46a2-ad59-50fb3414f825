"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _Html5Outlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/Html5Outlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var Html5Outlined = function Html5Outlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _Html5Outlined.default
  }));
};

/**![html5](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0NSA5Nmw2NiA3NDYuNkw1MTEuOCA5MjhsMjk5LjYtODUuNEw4NzguNyA5NkgxNDV6bTYxMC45IDcwMC42bC0yNDQuMSA2OS42LTI0NS4yLTY5LjYtNTYuNy02NDEuMmg2MDMuOGwtNTcuOCA2NDEuMnpNMjgxIDI0OWwxLjcgMjQuMyAyMi43IDI1My41aDIwNi41di0uMWgxMTIuOWwtMTEuNCAxMTguNUw1MTEgNjcyLjl2LjJoLS44bC0xMDIuNC0yNy43LTYuNS03My4yaC05MWwxMS4zIDE0NC43IDE4OC42IDUyaDEuN3YtLjRsMTg3LjctNTEuNyAxLjctMTYuMyAyMS4yLTI0Mi4yIDMuMi0yNC4zSDUxMXYuMkgzODkuOWwtOC4yLTk0LjJoMzUyLjFsMS43LTE5LjUgNC44LTQ3LjJMNzQyIDI0OUg1MTF6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(Html5Outlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'Html5Outlined';
}
var _default = exports.default = RefIcon;