{"rustc": 1842507548689473721, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 3704019409370514786, "deps": [[555019317135488525, "regex_automata", false, 12865914231163259433], [2779309023524819297, "aho_corasick", false, 550456252444882822], [9408802513701742484, "regex_syntax", false, 6110618689216125348], [15932120279885307830, "memchr", false, 6786945194398862742]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-b17bda4ed6f2f822\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}