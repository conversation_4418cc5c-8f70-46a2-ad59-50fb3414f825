"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FilePdfOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FilePdfOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FilePdfOutlined = function FilePdfOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FilePdfOutlined.default
  }));
};

/**![file-pdf](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzMS4zIDU3NC40bC4zLTEuNGM1LjgtMjMuOSAxMy4xLTUzLjcgNy40LTgwLjctMy44LTIxLjMtMTkuNS0yOS42LTMyLjktMzAuMi0xNS44LS43LTI5LjkgOC4zLTMzLjQgMjEuNC02LjYgMjQtLjcgNTYuOCAxMC4xIDk4LjYtMTMuNiAzMi40LTM1LjMgNzkuNS01MS4yIDEwNy41LTI5LjYgMTUuMy02OS4zIDM4LjktNzUuMiA2OC43LTEuMiA1LjUuMiAxMi41IDMuNSAxOC44IDMuNyA3IDkuNiAxMi40IDE2LjUgMTUgMyAxLjEgNi42IDIgMTAuOCAyIDE3LjYgMCA0Ni4xLTE0LjIgODQuMS03OS40IDUuOC0xLjkgMTEuOC0zLjkgMTcuNi01LjkgMjcuMi05LjIgNTUuNC0xOC44IDgwLjktMjMuMSAyOC4yIDE1LjEgNjAuMyAyNC44IDgyLjEgMjQuOCAyMS42IDAgMzAuMS0xMi44IDMzLjMtMjAuNSA1LjYtMTMuNSAyLjktMzAuNS02LjItMzkuNi0xMy4yLTEzLTQ1LjMtMTYuNC05NS4zLTEwLjItMjQuNi0xNS00MC43LTM1LjQtNTIuNC02NS44ek00MjEuNiA3MjYuM2MtMTMuOSAyMC4yLTI0LjQgMzAuMy0zMC4xIDM0LjcgNi43LTEyLjMgMTkuOC0yNS4zIDMwLjEtMzQuN3ptODcuNi0yMzUuNWM1LjIgOC45IDQuNSAzNS44LjUgNDkuNC00LjktMTkuOS01LjYtNDguMS0yLjctNTEuNC44LjEgMS41LjcgMi4yIDJ6bS0xLjYgMTIwLjVjMTAuNyAxOC41IDI0LjIgMzQuNCAzOS4xIDQ2LjItMjEuNiA0LjktNDEuMyAxMy01OC45IDIwLjItNC4yIDEuNy04LjMgMy40LTEyLjMgNSAxMy4zLTI0LjEgMjQuNC01MS40IDMyLjEtNzEuNHptMTU1LjYgNjUuNWMuMS4yLjIuNS0uNC45aC0uMmwtLjIuM2MtLjguNS05IDUuMy00NC4zLTguNiA0MC42LTEuOSA0NSA3LjMgNDUuMSA3LjR6bTE5MS40LTM4OC4yTDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FilePdfOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FilePdfOutlined';
}
var _default = exports.default = RefIcon;