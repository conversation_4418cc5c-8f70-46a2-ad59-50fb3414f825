import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-done](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OCAzMTJ2LTQ4YzAtNC40LTMuNi04LTgtOEgyOTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHptLTM5MiA4OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxODRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LThIMjk2em0zNzYgMTE2Yy0xMTkuMyAwLTIxNiA5Ni43LTIxNiAyMTZzOTYuNyAyMTYgMjE2IDIxNiAyMTYtOTYuNyAyMTYtMjE2LTk2LjctMjE2LTIxNi0yMTZ6bTEwNy41IDMyMy41Qzc1MC44IDg2OC4yIDcxMi42IDg4NCA2NzIgODg0cy03OC44LTE1LjgtMTA3LjUtNDQuNUM1MzUuOCA4MTAuOCA1MjAgNzcyLjYgNTIwIDczMnMxNS44LTc4LjggNDQuNS0xMDcuNUM1OTMuMiA1OTUuOCA2MzEuNCA1ODAgNjcyIDU4MHM3OC44IDE1LjggMTA3LjUgNDQuNUM4MDguMiA2NTMuMiA4MjQgNjkxLjQgODI0IDczMnMtMTUuOCA3OC44LTQ0LjUgMTA3LjV6TTc2MSA2NTZoLTQ0LjNjLTIuNiAwLTUgMS4yLTYuNSAzLjNsLTYzLjUgODcuOC0yMy4xLTMxLjlhNy45MiA3LjkyIDAgMDAtNi41LTMuM0g1NzNjLTYuNSAwLTEwLjMgNy40LTYuNSAxMi43bDczLjggMTAyLjFjMy4yIDQuNCA5LjcgNC40IDEyLjkgMGwxMTQuMi0xNThjMy45LTUuMy4xLTEyLjctNi40LTEyLjd6TTQ0MCA4NTJIMjA4VjE0OGg1NjB2MzQ0YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWMTA4YzAtMTcuNy0xNC4zLTMyLTMyLTMySDE2OGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Nzg0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDI3MmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
