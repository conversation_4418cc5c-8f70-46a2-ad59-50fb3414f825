use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize, FromRow)]
pub struct Account {
    pub id: i64,
    pub name: String,
    pub email: String,
    pub imap_server: String,
    pub imap_port: i32,
    pub username: String,
    pub password: String, // 加密存储
    pub use_tls: bool,
    pub created_at: String,
    pub updated_at: String,
    pub is_active: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateAccountRequest {
    pub name: String,
    pub email: String,
    pub imap_server: String,
    pub imap_port: i32,
    pub username: String,
    pub password: String,
    pub use_tls: bool,
    pub is_active: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateAccountRequest {
    pub name: Option<String>,
    pub email: Option<String>,
    pub imap_server: Option<String>,
    pub imap_port: Option<i32>,
    pub username: Option<String>,
    pub password: Option<String>,
    pub use_tls: Option<bool>,
    pub is_active: Option<bool>,
}

impl Account {
    pub fn new(req: CreateAccountRequest, encrypted_password: String) -> Self {
        let now = chrono::Utc::now().to_rfc3339();
        Self {
            id: 0, // 将由数据库自动生成
            name: req.name,
            email: req.email,
            imap_server: req.imap_server,
            imap_port: req.imap_port,
            username: req.username,
            password: encrypted_password,
            use_tls: req.use_tls,
            created_at: now.clone(),
            updated_at: now,
            is_active: req.is_active,
        }
    }

    pub fn update(&mut self, req: UpdateAccountRequest, encrypted_password: Option<String>) {
        if let Some(name) = req.name {
            self.name = name;
        }
        if let Some(email) = req.email {
            self.email = email;
        }
        if let Some(imap_server) = req.imap_server {
            self.imap_server = imap_server;
        }
        if let Some(imap_port) = req.imap_port {
            self.imap_port = imap_port;
        }
        if let Some(username) = req.username {
            self.username = username;
        }
        if let Some(password) = encrypted_password {
            self.password = password;
        }
        if let Some(use_tls) = req.use_tls {
            self.use_tls = use_tls;
        }
        if let Some(is_active) = req.is_active {
            self.is_active = is_active;
        }
        self.updated_at = chrono::Utc::now().to_rfc3339();
    }
}
