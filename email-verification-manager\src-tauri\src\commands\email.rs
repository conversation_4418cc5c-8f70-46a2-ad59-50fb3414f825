use crate::models::{<PERSON><PERSON>, <PERSON>ailWithAccount};
use crate::services::{AccountService, ImapService};
use tauri::command;

#[command]
pub async fn fetch_latest_emails(account_id: i64, count: Option<usize>) -> Result<Vec<Email>, String> {
    let count = count.unwrap_or(10);
    
    let account = AccountService::get_account_by_id(account_id)
        .await
        .map_err(|e| e.to_string())?;
    
    ImapService::fetch_latest_emails(&account, count)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_emails_by_account(account_id: i64, limit: Option<i64>) -> Result<Vec<Email>, String> {
    ImapService::get_emails_by_account(account_id, limit)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn refresh_all_accounts() -> Result<Vec<EmailWithAccount>, String> {
    let accounts = AccountService::get_active_accounts()
        .await
        .map_err(|e| e.to_string())?;
    
    let mut all_emails = Vec::new();
    
    for account in accounts {
        match ImapService::fetch_latest_emails(&account, 5).await {
            Ok(emails) => {
                for email in emails {
                    all_emails.push(EmailWithAccount {
                        id: email.id,
                        account_id: email.account_id,
                        subject: email.subject,
                        sender: email.sender,
                        body: email.body,
                        verification_code: email.verification_code,
                        received_at: email.received_at,
                        message_id: email.message_id,
                        account_name: account.name.clone(),
                        account_email: account.email.clone(),
                    });
                }
            }
            Err(e) => {
                eprintln!("Failed to fetch emails for account {}: {}", account.name, e);
            }
        }
    }
    
    // 按接收时间排序
    all_emails.sort_by(|a, b| b.received_at.cmp(&a.received_at));
    
    Ok(all_emails)
}
