import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Switch, Button, Select, Space, message } from 'antd';
import { TestConnectionOutlined } from '@ant-design/icons';
import { useAccounts } from '../../hooks/useAccounts';
import type { Account, CreateAccountRequest, EmailProvider } from '../../services/types';
import { EMAIL_PROVIDERS } from '../../services/types';

const { Option } = Select;

interface AccountFormProps {
  account?: Account | null;
  onSubmit: (accountData: CreateAccountRequest) => Promise<void>;
  onCancel: () => void;
}

const AccountForm: React.FC<AccountFormProps> = ({ account, onSubmit, onCancel }) => {
  const [form] = Form.useForm();
  const [testing, setTesting] = useState(false);
  const { testConnection } = useAccounts();

  useEffect(() => {
    if (account) {
      form.setFieldsValue({
        name: account.name,
        email: account.email,
        imap_server: account.imap_server,
        imap_port: account.imap_port,
        username: account.username,
        use_tls: account.use_tls,
        is_active: account.is_active,
      });
    } else {
      form.resetFields();
    }
  }, [account, form]);

  const handleProviderChange = (value: string) => {
    const provider = EMAIL_PROVIDERS.find(p => p.name === value);
    if (provider) {
      form.setFieldsValue({
        imap_server: provider.imap_server,
        imap_port: provider.imap_port,
        use_tls: provider.use_tls,
      });
    }
  };

  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields();
      setTesting(true);
      
      const testData: CreateAccountRequest = {
        name: values.name,
        email: values.email,
        imap_server: values.imap_server,
        imap_port: values.imap_port,
        username: values.username,
        password: values.password,
        use_tls: values.use_tls,
        is_active: values.is_active,
      };

      const result = await testConnection(testData);
      if (result) {
        message.success('连接测试成功！');
      } else {
        message.error('连接测试失败，请检查配置信息');
      }
    } catch (error) {
      message.error('连接测试失败');
    } finally {
      setTesting(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const accountData: CreateAccountRequest = {
        name: values.name,
        email: values.email,
        imap_server: values.imap_server,
        imap_port: values.imap_port,
        username: values.username,
        password: values.password,
        use_tls: values.use_tls,
        is_active: values.is_active,
      };
      
      await onSubmit(accountData);
    } catch (error) {
      // 错误处理由父组件处理
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        use_tls: true,
        is_active: true,
        imap_port: 993,
      }}
    >
      <Form.Item
        label="账号名称"
        name="name"
        rules={[{ required: true, message: '请输入账号名称' }]}
      >
        <Input placeholder="例如：我的Gmail账号" />
      </Form.Item>

      <Form.Item
        label="邮箱地址"
        name="email"
        rules={[
          { required: true, message: '请输入邮箱地址' },
          { type: 'email', message: '请输入有效的邮箱地址' }
        ]}
      >
        <Input placeholder="<EMAIL>" />
      </Form.Item>

      <Form.Item label="邮箱服务商">
        <Select
          placeholder="选择邮箱服务商（可选）"
          allowClear
          onChange={handleProviderChange}
        >
          {EMAIL_PROVIDERS.map(provider => (
            <Option key={provider.name} value={provider.name}>
              {provider.name}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        label="IMAP服务器"
        name="imap_server"
        rules={[{ required: true, message: '请输入IMAP服务器地址' }]}
      >
        <Input placeholder="imap.gmail.com" />
      </Form.Item>

      <Form.Item
        label="IMAP端口"
        name="imap_port"
        rules={[{ required: true, message: '请输入IMAP端口' }]}
      >
        <InputNumber min={1} max={65535} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        label="用户名"
        name="username"
        rules={[{ required: true, message: '请输入用户名' }]}
      >
        <Input placeholder="通常是邮箱地址" />
      </Form.Item>

      <Form.Item
        label="密码/应用专用密码"
        name="password"
        rules={[{ required: !account, message: '请输入密码或应用专用密码' }]}
      >
        <Input.Password placeholder="建议使用应用专用密码" />
      </Form.Item>

      <Form.Item
        label="使用TLS加密"
        name="use_tls"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="启用账号"
        name="is_active"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {account ? '更新' : '添加'}
          </Button>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button 
            icon={<TestConnectionOutlined />} 
            onClick={handleTestConnection}
            loading={testing}
          >
            测试连接
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default AccountForm;
