use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::sync::OnceLock;
use anyhow::Result;

static DB_POOL: OnceLock<Pool<Sqlite>> = OnceLock::new();

pub async fn init_database() -> Result<()> {
    // 获取应用数据目录
    let app_dir = tauri::api::path::app_data_dir(&tauri::Config::default())
        .ok_or_else(|| anyhow::anyhow!("Failed to get app data directory"))?;
    
    // 确保目录存在
    std::fs::create_dir_all(&app_dir)?;
    
    // 数据库文件路径
    let db_path = app_dir.join("email_verification.db");
    let db_url = format!("sqlite:{}", db_path.display());
    
    // 创建连接池
    let pool = SqlitePool::connect(&db_url).await?;
    
    // 运行迁移
    run_migrations(&pool).await?;
    
    // 存储全局连接池
    DB_POOL.set(pool).map_err(|_| anyhow::anyhow!("Failed to set database pool"))?;
    
    Ok(())
}

pub fn get_db_pool() -> &'static Pool<Sqlite> {
    DB_POOL.get().expect("Database not initialized")
}

async fn run_migrations(pool: &Pool<Sqlite>) -> Result<()> {
    // 创建账号表
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT NOT NULL UNIQUE,
            imap_server TEXT NOT NULL,
            imap_port INTEGER NOT NULL,
            username TEXT NOT NULL,
            password TEXT NOT NULL,
            use_tls BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // 创建邮件表
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS emails (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_id INTEGER NOT NULL,
            subject TEXT NOT NULL,
            sender TEXT NOT NULL,
            body TEXT NOT NULL,
            verification_code TEXT,
            received_at TEXT NOT NULL,
            message_id TEXT NOT NULL UNIQUE,
            FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // 创建索引
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_emails_account_id ON emails (account_id)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_emails_received_at ON emails (received_at)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_emails_message_id ON emails (message_id)")
        .execute(pool)
        .await?;

    Ok(())
}
