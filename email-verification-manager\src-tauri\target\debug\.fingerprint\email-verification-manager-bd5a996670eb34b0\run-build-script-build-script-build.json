{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5610300312719019757, "build_script_build", false, 15746185617506767244]], "local": [{"RerunIfChanged": {"output": "debug\\build\\email-verification-manager-bd5a996670eb34b0\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}