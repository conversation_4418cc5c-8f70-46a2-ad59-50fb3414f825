{"rustc": 1842507548689473721, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 3994016078406437691, "deps": [[7911289239703230891, "adler2", false, 367096289025387560]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-8987caa600ab1671\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}