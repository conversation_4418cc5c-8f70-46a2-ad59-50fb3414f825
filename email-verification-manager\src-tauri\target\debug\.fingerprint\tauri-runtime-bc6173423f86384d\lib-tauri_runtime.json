{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 13564516720960837522, "deps": [[3150220818285335163, "url", false, 6406480644215810995], [4381063397040571828, "webview2_com", false, 8020530905907141150], [4405182208873388884, "http", false, 11575052487955420745], [7653476968652377684, "windows", false, 1900602856807682700], [8008191657135824715, "thiserror", false, 12830748378722880387], [8292277814562636972, "tauri_utils", false, 16255483686337415295], [8319709847752024821, "uuid", false, 6688255699529898215], [8866577183823226611, "http_range", false, 8729541450533396755], [9689903380558560274, "serde", false, 12644071661126814469], [11693073011723388840, "raw_window_handle", false, 810186779276519523], [13208667028893622512, "rand", false, 13022999967511982451], [14162324460024849578, "build_script_build", false, 2084634521993563556], [16362055519698394275, "serde_json", false, 3264631440749833641]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-bc6173423f86384d\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}