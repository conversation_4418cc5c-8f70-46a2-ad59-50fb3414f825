"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _InsuranceOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/InsuranceOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var InsuranceOutlined = function InsuranceOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _InsuranceOutlined.default
  }));
};

/**![insurance](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0MS42IDMwNi44TDQwMyAyODguNmE2LjEgNi4xIDAgMDAtOC40IDMuN2MtMTcuNSA1OC41LTQ1LjIgMTEwLjEtODIuMiAxNTMuNmE2LjA1IDYuMDUgMCAwMC0xLjIgNS42bDEzLjIgNDMuNWMxLjMgNC40IDcgNS43IDEwLjIgMi40IDcuNy04LjEgMTUuNC0xNi45IDIzLjEtMjZWNjU2YzAgNC40IDMuNiA4IDggOEg0MDNjNC40IDAgOC0zLjYgOC04VjM5My4xYTQyOS4yIDQyOS4yIDAgMDAzMy42LTc5YzEtMi45LS4zLTYtMy03LjN6bTI2LjggOS4ydjEyNy4yYzAgNC40IDMuNiA4IDggOGg2NS45djE4LjZoLTk0LjljLTQuNCAwLTggMy42LTggOHYzNS42YzAgNC40IDMuNiA4IDggOGg1NS4xYy0xOS4xIDMwLjgtNDIuNCA1NS43LTcxIDc2YTYgNiAwIDAwLTEuNiA4LjFsMjIuOCAzNi41YzEuOSAzLjEgNi4yIDMuOCA4LjkgMS40IDMxLjYtMjYuOCA1OC43LTYyLjkgODAuNi0xMDcuNnYxMjBjMCA0LjQgMy42IDggOCA4aDM2LjJjNC40IDAgOC0zLjYgOC04VjUzNmMyMS4zIDQxLjcgNDcuNSA3Ny41IDc4LjEgMTA2LjkgMi42IDIuNSA2LjggMi4xIDguOS0uN2wyNi4zLTM1LjNjMi0yLjcgMS40LTYuNS0xLjItOC40LTMwLjUtMjIuNi01NC4yLTQ3LjgtNzIuMy03Ni45aDU5YzQuNCAwIDgtMy42IDgtOFY0NzhjMC00LjQtMy42LTgtOC04aC05OC44di0xOC42aDY2LjdjNC40IDAgOC0zLjYgOC04VjMxNmMwLTQuNC0zLjYtOC04LThINDc2LjRjLTQuNCAwLTggMy42LTggOHptNTEuNSA0Mi44aDk3Ljl2NDEuNmgtOTcuOXYtNDEuNnptMzQ3LTE4OC45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(InsuranceOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InsuranceOutlined';
}
var _default = exports.default = RefIcon;