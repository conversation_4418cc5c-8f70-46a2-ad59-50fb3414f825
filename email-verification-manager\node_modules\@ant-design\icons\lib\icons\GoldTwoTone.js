"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _GoldTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/GoldTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var GoldTwoTone = function GoldTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _GoldTwoTone.default
  }));
};

/**![gold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQzNS43IDU1OC43Yy0uNi0zLjktNC02LjctNy45LTYuN0gxNjYuMmMtMy45IDAtNy4zIDIuOC03LjkgNi43bC00MC4yIDI0OGMtLjEuNC0uMS45LS4xIDEuMyAwIDQuNCAzLjYgOCA4IDhoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDh6TTE5Ni41IDc0OGwyMC43LTEyOGgxNTkuNWwyMC43IDEyOEgxOTYuNXptNzA5LjQgNTguN2wtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDU5Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjMtLjcgNy4zLTQuOCA2LjYtOS4yek02MjYuNSA3NDhsMjAuNy0xMjhoMTU5LjVsMjAuNyAxMjhINjI2LjV6TTM0MiA0NzJoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDM4Mi4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOHptOTEuMi0xOTZoMTU5LjVsMjAuNyAxMjhoLTIwMWwyMC44LTEyOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTU5Mi43IDI3Nkg0MzMuMmwtMjAuOCAxMjhoMjAxek0yMTcuMiA2MjBsLTIwLjcgMTI4aDIwMC45bC0yMC43LTEyOHptNDMwIDBsLTIwLjcgMTI4aDIwMC45bC0yMC43LTEyOHoiIGZpbGw9IiNlNmY0ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(GoldTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GoldTwoTone';
}
var _default = exports.default = RefIcon;