import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![import](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNODgwIDkxMkgxNDRjLTE3LjcgMC0zMi0xNC4zLTMyLTMyVjE0NGMwLTE3LjcgMTQuMy0zMiAzMi0zMmgzNjBjNC40IDAgOCAzLjYgOCA4djU2YzAgNC40LTMuNiA4LTggOEgxODR2NjU2aDY1NlY1MjBjMC00LjQgMy42LTggOC04aDU2YzQuNCAwIDggMy42IDggOHYzNjBjMCAxNy43LTE0LjMgMzItMzIgMzJ6TTY1My4zIDQyNC42bDUyLjIgNTIuMmE4LjAxIDguMDEgMCAwMS00LjcgMTMuNmwtMTc5LjQgMjFjLTUuMS42LTkuNS0zLjctOC45LTguOWwyMS0xNzkuNGMuOC02LjYgOC45LTkuNCAxMy42LTQuN2w1Mi40IDUyLjQgMjU2LjItMjU2LjJjMy4xLTMuMSA4LjItMy4xIDExLjMgMGw0Mi40IDQyLjRjMy4xIDMuMSAzLjEgOC4yIDAgMTEuM0w2NTMuMyA0MjQuNnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
