import React, { useState } from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import MainLayout from './components/Layout/MainLayout';
import AccountManagement from './pages/AccountManagement/AccountManagement';
import VerificationCodes from './pages/VerificationCodes/VerificationCodes';
import './App.css';

type PageType = 'accounts' | 'codes';

function App() {
  const [currentPage, setCurrentPage] = useState<PageType>('accounts');

  const renderPage = () => {
    switch (currentPage) {
      case 'accounts':
        return <AccountManagement />;
      case 'codes':
        return <VerificationCodes />;
      default:
        return <AccountManagement />;
    }
  };

  return (
    <ConfigProvider locale={zhCN}>
      <MainLayout currentPage={currentPage} onPageChange={setCurrentPage}>
        {renderPage()}
      </MainLayout>
    </ConfigProvider>
  );
}

export default App;
