import React, { useState } from 'react';
import { ConfigProvider, Button, Layout, Typography } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

const { Content } = Layout;
const { Title } = Typography;

function App() {
  const [count, setCount] = useState(0);

  return (
    <ConfigProvider locale={zhCN}>
      <Layout style={{ minHeight: '100vh' }}>
        <Content style={{ padding: '50px', textAlign: 'center' }}>
          <Title level={1}>邮箱验证码管理工具</Title>
          <Title level={3}>应用正在运行！</Title>
          <div style={{ margin: '20px 0' }}>
            <Button
              type="primary"
              size="large"
              onClick={() => setCount(count + 1)}
            >
              点击测试: {count}
            </Button>
          </div>
          <p>如果你能看到这个页面，说明Tauri桌面应用已经成功启动！</p>
        </Content>
      </Layout>
    </ConfigProvider>
  );
}

export default App;
