import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![left-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xMDQgMzE2LjljMCAxMC4yLTQuOSAxOS45LTEzLjIgMjUuOUw0NTcuNCA1MTJsMTQ1LjQgMTA1LjJjOC4zIDYgMTMuMiAxNS42IDEzLjIgMjUuOVY2OTBjMCA2LjUtNy40IDEwLjMtMTIuNyA2LjVsLTI0Ni0xNzhhNy45NSA3Ljk1IDAgMDEwLTEyLjlsMjQ2LTE3OGE4IDggMCAwMTEyLjcgNi41djQ2Ljh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
