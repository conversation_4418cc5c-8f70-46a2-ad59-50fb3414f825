import { useState, useCallback } from 'react';
import { emailApi, handleApiError } from '../services/tauri-api';
import type { Email, EmailWithAccount } from '../services/types';

export const useEmails = () => {
  const [emails, setEmails] = useState<EmailWithAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshAllEmails = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await emailApi.refreshAllAccounts();
      setEmails(result);
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Failed to refresh emails:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchEmailsByAccount = useCallback(async (accountId: number, limit?: number): Promise<Email[]> => {
    setLoading(true);
    setError(null);
    try {
      const result = await emailApi.getEmailsByAccount(accountId, limit);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchLatestEmails = useCallback(async (accountId: number, count?: number): Promise<Email[]> => {
    setLoading(true);
    setError(null);
    try {
      const result = await emailApi.fetchLatestEmails(accountId, count);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    emails,
    loading,
    error,
    clearError,
    refreshAllEmails,
    fetchEmailsByAccount,
    fetchLatestEmails,
  };
};
