"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _InsertRowBelowOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/InsertRowBelowOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var InsertRowBelowOutlined = function InsertRowBelowOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _InsertRowBelowOutlined.default
  }));
};

/**![insert-row-below](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05MDQgNzY4SDEyMGMtNC40IDAtOCAzLjYtOCA4djgwYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di04MGMwLTQuNC0zLjYtOC04LTh6bS0yNS4zLTYwOEgxNDUuM2MtMTguNCAwLTMzLjMgMTQuMy0zMy4zIDMydjQ2NGMwIDE3LjcgMTQuOSAzMiAzMy4zIDMyaDczMy4zYzE4LjQgMCAzMy4zLTE0LjMgMzMuMy0zMlYxOTJjLjEtMTcuNy0xNC44LTMyLTMzLjItMzJ6TTM2MCA2MTZIMTg0VjQ1NmgxNzZ2MTYwem0wLTIyNEgxODRWMjMyaDE3NnYxNjB6bTI0MCAyMjRINDI0VjQ1NmgxNzZ2MTYwem0wLTIyNEg0MjRWMjMyaDE3NnYxNjB6bTI0MCAyMjRINjY0VjQ1NmgxNzZ2MTYwem0wLTIyNEg2NjRWMjMyaDE3NnYxNjB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(InsertRowBelowOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InsertRowBelowOutlined';
}
var _default = exports.default = RefIcon;