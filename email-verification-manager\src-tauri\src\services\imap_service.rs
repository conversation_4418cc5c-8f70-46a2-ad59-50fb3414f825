use crate::database::get_db_pool;
use crate::models::{Account, Email};
use crate::services::AccountService;
use anyhow::{anyhow, Result};
use imap::Session;
use native_tls::{TlsConnector, TlsStream};
use std::net::TcpStream;
use sqlx::Row;

pub struct ImapService;

impl ImapService {
    pub async fn test_connection(account: &Account) -> Result<bool> {
        let password = AccountService::get_decrypted_password(account).await?;
        
        match Self::create_session(account, &password).await {
            Ok(mut session) => {
                session.logout().ok();
                Ok(true)
            }
            Err(_) => Ok(false),
        }
    }

    pub async fn fetch_latest_emails(account: &Account, count: usize) -> Result<Vec<Email>> {
        let password = AccountService::get_decrypted_password(account).await?;
        let mut session = Self::create_session(account, &password).await?;
        
        // 选择INBOX
        session.select("INBOX")?;
        
        // 搜索最新的邮件
        let messages = session.search("ALL")?;
        
        if messages.is_empty() {
            return Ok(Vec::new());
        }
        
        // 获取最新的几封邮件
        let messages_vec: Vec<u32> = messages.into_iter().collect();
        let start = if messages_vec.len() > count {
            messages_vec.len() - count
        } else {
            0
        };
        let latest_messages = &messages_vec[start..];
        
        let mut emails = Vec::new();
        
        for &msg_id in latest_messages {
            if let Ok(email) = Self::fetch_email(&mut session, account.id, msg_id).await {
                emails.push(email);
            }
        }
        
        // 登出
        session.logout()?;
        
        // 保存到数据库
        Self::save_emails_to_db(&emails).await?;
        
        Ok(emails)
    }

    async fn create_session(account: &Account, password: &str) -> Result<Session<TlsStream<TcpStream>>> {
        let tls = TlsConnector::builder().build()?;
        
        let client = if account.use_tls {
            imap::connect((account.imap_server.as_str(), account.imap_port as u16), &account.imap_server, &tls)?
        } else {
            return Err(anyhow!("Non-TLS connections not supported in this implementation"));
        };

        let session = client
            .login(&account.username, password)
            .map_err(|e| anyhow!("Login failed: {:?}", e.0))?;

        Ok(session)
    }

    async fn fetch_email(session: &mut Session<TlsStream<TcpStream>>, account_id: i64, msg_id: u32) -> Result<Email> {
        let messages = session.fetch(msg_id.to_string(), "RFC822")?;
        
        if messages.is_empty() {
            return Err(anyhow!("Message not found"));
        }
        
        let message = &messages[0];
        let body = message.body().unwrap_or_default();
        
        // 解析邮件
        let parsed = mailparse::parse_mail(body)?;
        
        let subject = parsed.headers.iter()
            .find(|h| h.get_key().eq_ignore_ascii_case("subject"))
            .map(|h| h.get_value())
            .unwrap_or_default();
            
        let from = parsed.headers.iter()
            .find(|h| h.get_key().eq_ignore_ascii_case("from"))
            .map(|h| h.get_value())
            .unwrap_or_default();
            
        let message_id = parsed.headers.iter()
            .find(|h| h.get_key().eq_ignore_ascii_case("message-id"))
            .map(|h| h.get_value())
            .unwrap_or_else(|| format!("{}_{}", account_id, msg_id));
            
        let date = parsed.headers.iter()
            .find(|h| h.get_key().eq_ignore_ascii_case("date"))
            .map(|h| h.get_value())
            .unwrap_or_else(|| chrono::Utc::now().to_rfc3339());

        // 提取邮件正文
        let email_body = Self::extract_text_body(&parsed);
        
        // 创建邮件对象
        let mut email = Email::new(
            account_id,
            subject,
            from,
            email_body,
            message_id,
            date,
        );
        
        // 提取验证码
        email.extract_verification_code();
        
        Ok(email)
    }

    fn extract_text_body(parsed: &mailparse::ParsedMail) -> String {
        if parsed.ctype.mimetype == "text/plain" {
            parsed.get_body().unwrap_or_default()
        } else if parsed.ctype.mimetype == "text/html" {
            // 简单的HTML到文本转换
            let html = parsed.get_body().unwrap_or_default();
            Self::html_to_text(&html)
        } else if parsed.ctype.mimetype.starts_with("multipart/") {
            // 处理多部分邮件
            for subpart in &parsed.subparts {
                if subpart.ctype.mimetype == "text/plain" {
                    return subpart.get_body().unwrap_or_default();
                }
            }
            // 如果没有纯文本部分，尝试HTML
            for subpart in &parsed.subparts {
                if subpart.ctype.mimetype == "text/html" {
                    let html = subpart.get_body().unwrap_or_default();
                    return Self::html_to_text(&html);
                }
            }
            String::new()
        } else {
            String::new()
        }
    }

    fn html_to_text(html: &str) -> String {
        // 简单的HTML标签移除
        let re = regex::Regex::new(r"<[^>]*>").unwrap();
        let text = re.replace_all(html, " ");
        
        // 解码HTML实体
        text.replace("&nbsp;", " ")
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&amp;", "&")
            .replace("&quot;", "\"")
            .trim()
            .to_string()
    }

    async fn save_emails_to_db(emails: &[Email]) -> Result<()> {
        let pool = get_db_pool();
        
        for email in emails {
            // 检查邮件是否已存在
            let exists = sqlx::query("SELECT COUNT(*) as count FROM emails WHERE message_id = ?")
                .bind(&email.message_id)
                .fetch_one(pool)
                .await?
                .get::<i64, _>("count") > 0;
                
            if !exists {
                sqlx::query(
                    r#"
                    INSERT INTO emails (account_id, subject, sender, body, verification_code, received_at, message_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    "#,
                )
                .bind(email.account_id)
                .bind(&email.subject)
                .bind(&email.sender)
                .bind(&email.body)
                .bind(&email.verification_code)
                .bind(&email.received_at)
                .bind(&email.message_id)
                .execute(pool)
                .await?;
            }
        }
        
        Ok(())
    }

    pub async fn get_emails_by_account(account_id: i64, limit: Option<i64>) -> Result<Vec<Email>> {
        let pool = get_db_pool();
        let limit = limit.unwrap_or(50);
        
        let emails = sqlx::query_as::<_, Email>(
            "SELECT * FROM emails WHERE account_id = ? ORDER BY received_at DESC LIMIT ?"
        )
        .bind(account_id)
        .bind(limit)
        .fetch_all(pool)
        .await?;

        Ok(emails)
    }
}
