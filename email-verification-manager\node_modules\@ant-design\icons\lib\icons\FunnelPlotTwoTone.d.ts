import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![funnel-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQyMC42IDc5OGgxODIuOVY2NTBINDIwLjZ6TTI5Ny43IDM3NGg0MjguNmw4NS0xNDhIMjEyLjd6bTExMy4yIDE5Ny40bDguNCAxNC42aDE4NS4zbDguNC0xNC42TDY4OS42IDQzOEgzMzQuNHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTg4MC4xIDE1NEgxNDMuOWMtMjQuNSAwLTM5LjggMjYuNy0yNy41IDQ4TDM0OSA2MDcuNFY4MzhjMCAxNy43IDE0LjIgMzIgMzEuOCAzMmgyNjIuNGMxNy42IDAgMzEuOC0xNC4zIDMxLjgtMzJWNjA3LjRMOTA3LjcgMjAyYzEyLjItMjEuMy0zLjEtNDgtMjcuNi00OHpNNjAzLjUgNzk4SDQyMC42VjY1MGgxODIuOXYxNDh6bTkuNS0yMjYuNmwtOC40IDE0LjZINDE5LjNsLTguNC0xNC42TDMzNC40IDQzOGgzNTUuMkw2MTMgNTcxLjR6TTcyNi4zIDM3NEgyOTcuN2wtODUtMTQ4aDU5OC42bC04NSAxNDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
