"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_decorators = void 0;
const base_config_1 = require("./base-config");
const decorators_1 = require("./decorators");
const es2015_symbol_1 = require("./es2015.symbol");
exports.esnext_decorators = {
    ...es2015_symbol_1.es2015_symbol,
    ...decorators_1.decorators,
    SymbolConstructor: base_config_1.TYPE,
    Function: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.decorators.js.map