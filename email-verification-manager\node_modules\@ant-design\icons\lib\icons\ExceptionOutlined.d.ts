import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![exception](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OCAzMTJ2LTQ4YzAtNC40LTMuNi04LTgtOEgyOTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHptLTM5MiA4OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxODRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LThIMjk2em0zNzYgMTE2Yy0xMTkuMyAwLTIxNiA5Ni43LTIxNiAyMTZzOTYuNyAyMTYgMjE2IDIxNiAyMTYtOTYuNyAyMTYtMjE2LTk2LjctMjE2LTIxNi0yMTZ6bTEwNy41IDMyMy41Qzc1MC44IDg2OC4yIDcxMi42IDg4NCA2NzIgODg0cy03OC44LTE1LjgtMTA3LjUtNDQuNUM1MzUuOCA4MTAuOCA1MjAgNzcyLjYgNTIwIDczMnMxNS44LTc4LjggNDQuNS0xMDcuNUM1OTMuMiA1OTUuOCA2MzEuNCA1ODAgNjcyIDU4MHM3OC44IDE1LjggMTA3LjUgNDQuNUM4MDguMiA2NTMuMiA4MjQgNjkxLjQgODI0IDczMnMtMTUuOCA3OC44LTQ0LjUgMTA3LjV6TTY0MCA4MTJhMzIgMzIgMCAxMDY0IDAgMzIgMzIgMCAxMC02NCAwem0xMi02NGg0MGM0LjQgMCA4LTMuNiA4LThWNjI4YzAtNC40LTMuNi04LTgtOGgtNDBjLTQuNCAwLTggMy42LTggOHYxMTJjMCA0LjQgMy42IDggOCA4ek00NDAgODUySDIwOFYxNDhoNTYwdjM0NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjEwOGMwLTE3LjctMTQuMy0zMi0zMi0zMkgxNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjc4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyNzJjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
