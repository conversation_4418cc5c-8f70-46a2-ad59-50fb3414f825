"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DownSquareFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DownSquareFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DownSquareFilled = function DownSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DownSquareFilled.default
  }));
};

/**![down-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjk2LjUgNDEyLjdsLTE3OCAyNDZhNy45NSA3Ljk1IDAgMDEtMTIuOSAwbC0xNzgtMjQ2Yy0zLjgtNS4zIDAtMTIuNyA2LjUtMTIuN0gzODFjMTAuMiAwIDE5LjkgNC45IDI1LjkgMTMuMkw1MTIgNTU4LjZsMTA1LjItMTQ1LjRjNi04LjMgMTUuNi0xMy4yIDI1LjktMTMuMkg2OTBjNi41IDAgMTAuMyA3LjQgNi41IDEyLjd6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(DownSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DownSquareFilled';
}
var _default = exports.default = RefIcon;