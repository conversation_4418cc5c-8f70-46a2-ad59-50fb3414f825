{"name": "email-verification-manager", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@tauri-apps/api": "^1.5.3", "antd": "^5.12.8", "dayjs": "^1.11.13", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@tauri-apps/cli": "^1.5.8", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}