use aes_gcm::{
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ni<PERSON>, Os<PERSON>ng},
    Aes256Gcm, Key, Nonce,
};
use anyhow::{anyhow, Result};
use base64::{engine::general_purpose, Engine as _};
use std::sync::OnceLock;

static CRYPTO_SERVICE: OnceLock<CryptoService> = OnceLock::new();

pub struct CryptoService {
    cipher: Aes256Gcm,
}

impl CryptoService {
    pub fn init() -> Result<()> {
        // 在实际应用中，这个密钥应该从安全的地方获取
        // 比如用户密码派生、系统密钥库等
        let key = Self::get_or_create_master_key()?;
        let cipher = Aes256Gcm::new(&key);
        
        let service = CryptoService { cipher };
        CRYPTO_SERVICE.set(service).map_err(|_| anyhow!("Failed to initialize crypto service"))?;
        
        Ok(())
    }

    pub fn get() -> &'static CryptoService {
        CRYPTO_SERVICE.get().expect("Crypto service not initialized")
    }

    fn get_or_create_master_key() -> Result<Key<Aes256Gcm>> {
        // 在实际应用中，应该从安全存储中获取或生成密钥
        // 这里为了演示，使用固定的密钥派生方法
        let app_dir = tauri::api::path::app_data_dir(&tauri::Config::default())
            .ok_or_else(|| anyhow!("Failed to get app data directory"))?;
        
        let key_file = app_dir.join("master.key");
        
        if key_file.exists() {
            // 读取现有密钥
            let key_data = std::fs::read(&key_file)?;
            if key_data.len() != 32 {
                return Err(anyhow!("Invalid key file"));
            }
            Ok(*Key::<Aes256Gcm>::from_slice(&key_data))
        } else {
            // 生成新密钥
            std::fs::create_dir_all(&app_dir)?;
            let key = Aes256Gcm::generate_key(OsRng);
            std::fs::write(&key_file, &key)?;
            Ok(key)
        }
    }

    pub fn encrypt(&self, plaintext: &str) -> Result<String> {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self.cipher
            .encrypt(&nonce, plaintext.as_bytes())
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // 将nonce和密文组合
        let mut result = nonce.to_vec();
        result.extend_from_slice(&ciphertext);
        
        Ok(general_purpose::STANDARD.encode(result))
    }

    pub fn decrypt(&self, encrypted: &str) -> Result<String> {
        let data = general_purpose::STANDARD.decode(encrypted)?;
        
        if data.len() < 12 {
            return Err(anyhow!("Invalid encrypted data"));
        }
        
        let (nonce_bytes, ciphertext) = data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);
        
        let plaintext = self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;
        
        String::from_utf8(plaintext).map_err(|e| anyhow!("Invalid UTF-8: {}", e))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encrypt_decrypt() {
        let service = CryptoService {
            cipher: Aes256Gcm::new(&Aes256Gcm::generate_key(OsRng)),
        };

        let plaintext = "test password 123";
        let encrypted = service.encrypt(plaintext).unwrap();
        let decrypted = service.decrypt(&encrypted).unwrap();

        assert_eq!(plaintext, decrypted);
    }
}
