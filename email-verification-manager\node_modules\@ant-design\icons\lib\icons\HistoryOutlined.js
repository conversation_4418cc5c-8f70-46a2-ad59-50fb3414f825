"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _HistoryOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/HistoryOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var HistoryOutlined = function HistoryOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _HistoryOutlined.default
  }));
};

/**![history](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNi4xIDI3M0g0ODhjLTQuNCAwLTggMy42LTggOHYyNzUuM2MwIDIuNiAxLjIgNSAzLjMgNi41bDE2NS4zIDEyMC43YzMuNiAyLjYgOC42IDEuOSAxMS4yLTEuN2wyOC42LTM5YzIuNy0zLjcgMS45LTguNy0xLjctMTEuMkw1NDQuMSA1MjguNVYyODFjMC00LjQtMy42LTgtOC04em0yMTkuOCA3NS4ybDE1Ni44IDM4LjNjNSAxLjIgOS45LTIuNiA5LjktNy43bC44LTE2MS41YzAtNi43LTcuNy0xMC41LTEyLjktNi4zTDc1Mi45IDMzNC4xYTggOCAwIDAwMyAxNC4xem0xNjcuNyAzMDEuMWwtNTYuNy0xOS41YTggOCAwIDAwLTEwLjEgNC44Yy0xLjkgNS4xLTMuOSAxMC4xLTYgMTUuMS0xNy44IDQyLjEtNDMuMyA4MC03NS45IDExMi41YTM1MyAzNTMgMCAwMS0xMTIuNSA3NS45IDM1Mi4xOCAzNTIuMTggMCAwMS0xMzcuNyAyNy44Yy00Ny44IDAtOTQuMS05LjMtMTM3LjctMjcuOGEzNTMgMzUzIDAgMDEtMTEyLjUtNzUuOWMtMzIuNS0zMi41LTU4LTcwLjQtNzUuOS0xMTIuNUEzNTMuNDQgMzUzLjQ0IDAgMDExNzEgNTEyYzAtNDcuOCA5LjMtOTQuMiAyNy44LTEzNy44IDE3LjgtNDIuMSA0My4zLTgwIDc1LjktMTEyLjVhMzUzIDM1MyAwIDAxMTEyLjUtNzUuOUM0MzAuNiAxNjcuMyA0NzcgMTU4IDUyNC44IDE1OHM5NC4xIDkuMyAxMzcuNyAyNy44QTM1MyAzNTMgMCAwMTc3NSAyNjEuN2MxMC4yIDEwLjMgMTkuOCAyMSAyOC42IDMyLjNsNTkuOC00Ni44Qzc4NC43IDE0Ni42IDY2Mi4yIDgxLjkgNTI0LjYgODIgMjg1IDgyLjEgOTIuNiAyNzYuNyA5NSA1MTYuNCA5Ny40IDc1MS45IDI4OC45IDk0MiA1MjQuOCA5NDJjMTg1LjUgMCAzNDMuNS0xMTcuNiA0MDMuNy0yODIuMyAxLjUtNC4yLS43LTguOS00LjktMTAuNHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(HistoryOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HistoryOutlined';
}
var _default = exports.default = RefIcon;