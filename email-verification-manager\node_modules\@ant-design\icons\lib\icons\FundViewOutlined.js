"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FundViewOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FundViewOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FundViewOutlined = function FundViewOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FundViewOutlined.default
  }));
};

/**![fund-view](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NTYgNjg2LjVsLS4xLS4xLS4xLS4xQzkxMS43IDU5MyA4NDMuNCA1NDUgNzUyLjUgNTQ1cy0xNTkuMiA0OC4xLTIwMy40IDE0MS4zdi4xYTQyLjkyIDQyLjkyIDAgMDAwIDM2LjRDNTkzLjMgODE2IDY2MS42IDg2NCA3NTIuNSA4NjRzMTU5LjItNDguMSAyMDMuNC0xNDEuM2M1LjQtMTEuNSA1LjQtMjQuOC4xLTM2LjJ6TTc1Mi41IDgwMGMtNjIuMSAwLTEwNy40LTMwLTE0MS4xLTk1LjVDNjQ1IDYzOSA2OTAuNCA2MDkgNzUyLjUgNjA5YzYyLjEgMCAxMDcuNCAzMCAxNDEuMSA5NS41Qzg2MCA3NzAgODE0LjYgODAwIDc1Mi41IDgwMHoiIC8+PHBhdGggZD0iTTY5NyA3MDVhNTYgNTYgMCAxMDExMiAwIDU2IDU2IDAgMTAtMTEyIDB6TTEzNiAyMzJoNzA0djI1M2g3MlYxOTJjMC0xNy43LTE0LjMtMzItMzItMzJIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUyMGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzNTJ2LTcySDEzNlYyMzJ6IiAvPjxwYXRoIGQ9Ik03MjQuOSAzMzguMWwtMzYuOC0zNi44YTguMDMgOC4wMyAwIDAwLTExLjMgMEw0OTMgNDg1LjNsLTg2LjEtODYuMmE4LjAzIDguMDMgMCAwMC0xMS4zIDBMMjUxLjMgNTQzLjRhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzYuOCAzNi44YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsMTAxLjgtMTAxLjggODYuMSA4Ni4yYzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsMjI2LjMtMjI2LjVjMy4yLTMuMSAzLjItOC4yIDAtMTEuM3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FundViewOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FundViewOutlined';
}
var _default = exports.default = RefIcon;