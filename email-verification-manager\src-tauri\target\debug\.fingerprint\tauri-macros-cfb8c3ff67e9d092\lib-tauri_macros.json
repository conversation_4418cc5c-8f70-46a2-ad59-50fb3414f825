{"rustc": 1842507548689473721, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 11919998434713665695, "deps": [[2713742371683562785, "syn", false, 7111228983072178131], [3060637413840920116, "proc_macro2", false, 3511945678907558886], [8292277814562636972, "tauri_utils", false, 14122193339029557105], [13077543566650298139, "heck", false, 13910211334552500758], [17492769205600034078, "tauri_codegen", false, 14241476255532413451], [17990358020177143287, "quote", false, 12892848900342196043]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-cfb8c3ff67e9d092\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}