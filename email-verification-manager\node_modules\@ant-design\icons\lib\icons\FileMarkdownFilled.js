"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FileMarkdownFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FileMarkdownFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FileMarkdownFilled = function FileMarkdownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FileMarkdownFilled.default
  }));
};

/**![file-markdown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTQyNi4xMyA2MDAuOTNsNTkuMTEgMTMyLjk3YTE2IDE2IDAgMDAxNC42MiA5LjVoMjQuMDZhMTYgMTYgMCAwMDE0LjYzLTkuNTFsNTkuMS0xMzMuMzVWNzU4YTE2IDE2IDAgMDAxNi4wMSAxNkg2NDFhMTYgMTYgMCAwMDE2LTE2VjQ4NmExNiAxNiAwIDAwLTE2LTE2aC0zNC43NWExNiAxNiAwIDAwLTE0LjY3IDkuNjJMNTEyLjEgNjYyLjJsLTc5LjQ4LTE4Mi41OWExNiAxNiAwIDAwLTE0LjY3LTkuNjFIMzgzYTE2IDE2IDAgMDAtMTYgMTZ2MjcyYTE2IDE2IDAgMDAxNiAxNmgyNy4xM2ExNiAxNiAwIDAwMTYtMTZWNjAwLjkzeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileMarkdownFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileMarkdownFilled';
}
var _default = exports.default = RefIcon;