"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _EyeInvisibleFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/EyeInvisibleFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var EyeInvisibleFilled = function EyeInvisibleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _EyeInvisibleFilled.default
  }));
};

/**![eye-invisible](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUwOCA2MjRhMTEyIDExMiAwIDAwMTEyLTExMmMwLTMuMjgtLjE1LTYuNTMtLjQzLTkuNzRMNDk4LjI2IDYyMy41N2MzLjIxLjI4IDYuNDUuNDMgOS43NC40M3ptMzcwLjcyLTQ1OC40NEw4MzYgMTIyLjg4YTggOCAwIDAwLTExLjMxIDBMNzE1LjM3IDIzMi4yM1E2MjQuOTEgMTg2IDUxMiAxODZxLTI4OC4zIDAtNDMwLjIgMzAwLjNhNjAuMyA2MC4zIDAgMDAwIDUxLjVxNTYuNyAxMTkuNDMgMTM2LjU1IDE5MS40NUwxMTIuNTYgODM1YTggOCAwIDAwMCAxMS4zMUwxNTUuMjUgODg5YTggOCAwIDAwMTEuMzEgMGw3MTIuMTYtNzEyLjEyYTggOCAwIDAwMC0xMS4zMnpNMzMyIDUxMmExNzYgMTc2IDAgMDEyNTguODgtMTU1LjI4bC00OC42MiA0OC42MmExMTIuMDggMTEyLjA4IDAgMDAtMTQwLjkyIDE0MC45MmwtNDguNjIgNDguNjJBMTc1LjA5IDE3NS4wOSAwIDAxMzMyIDUxMnoiIC8+PHBhdGggZD0iTTk0Mi4yIDQ4Ni4yUTg4OS40IDM3NSA4MTYuNTEgMzA0Ljg1TDY3Mi4zNyA0NDlBMTc2LjA4IDE3Ni4wOCAwIDAxNDQ1IDY3Ni4zN0wzMjIuNzQgNzk4LjYzUTQwNy44MiA4MzggNTEyIDgzOHEyODguMyAwIDQzMC4yLTMwMC4zYTYwLjI5IDYwLjI5IDAgMDAwLTUxLjV6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(EyeInvisibleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'EyeInvisibleFilled';
}
var _default = exports.default = RefIcon;