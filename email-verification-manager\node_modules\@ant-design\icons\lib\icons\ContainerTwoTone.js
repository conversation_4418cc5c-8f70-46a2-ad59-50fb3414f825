"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ContainerTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ContainerTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ContainerTwoTone = function ContainerTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ContainerTwoTone.default
  }));
};

/**![container](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYzNSA3NzEuN2MtMzQuNSAyOC42LTc4LjIgNDQuMy0xMjMgNDQuM3MtODguNS0xNS44LTEyMy00NC4zYTE5NC4wMiAxOTQuMDIgMCAwMS01OS4xLTg0LjdIMjMydjIwMWg1NjBWNjg3aC05Ny45Yy0xMS42IDMyLjgtMzIgNjIuMy01OS4xIDg0Ljd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0zMjAgNTAxaDM4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgzMjBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik04MzIgNjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgODI0SDIzMlY2ODdoOTcuOWMxMS42IDMyLjggMzIgNjIuMyA1OS4xIDg0LjcgMzQuNSAyOC41IDc4LjIgNDQuMyAxMjMgNDQuM3M4OC41LTE1LjcgMTIzLTQ0LjNjMjcuMS0yMi40IDQ3LjUtNTEuOSA1OS4xLTg0LjdINzkydjIwMXptMC0yNjRINjQzLjZsLTUuMiAyNC43QzYyNi40IDcwOC41IDU3My4yIDc1MiA1MTIgNzUycy0xMTQuNC00My41LTEyNi41LTEwMy4zbC01LjItMjQuN0gyMzJWMTM2aDU2MHY0ODh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0zMjAgMzQxaDM4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgzMjBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ContainerTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ContainerTwoTone';
}
var _default = exports.default = RefIcon;