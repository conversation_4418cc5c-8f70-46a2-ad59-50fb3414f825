"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DeliveredProcedureOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DeliveredProcedureOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DeliveredProcedureOutlined = function DeliveredProcedureOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DeliveredProcedureOutlined.default
  }));
};

/**![delivered-procedure](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02MzIgNjk4LjNsMTQxLjktMTEyYTggOCAwIDAwMC0xMi42TDYzMiA0NjEuN2MtNS4zLTQuMi0xMy0uNC0xMyA2LjN2NzZIMjk1Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDMyNHY3NmMwIDYuNyA3LjggMTAuNCAxMyA2LjN6bTI2MS4zLTQwNUw3MzAuNyAxMzAuN2MtNy41LTcuNS0xNi43LTEzLTI2LjctMTZWMTEySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Mjc4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWMTg0aDEzNnYxMzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoMzIwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIwNS44bDEzNiAxMzZWNDIyYzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTgzLjVjMC0xNy02LjctMzMuMi0xOC43LTQ1LjJ6TTY0MCAyODhIMzg0VjE4NGgyNTZ2MTA0em0yNjQgNDM2aC01NmMtNC40IDAtOCAzLjYtOCA4djEwOEgxODRWNzMyYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHYxNDhjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjczMmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(DeliveredProcedureOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DeliveredProcedureOutlined';
}
var _default = exports.default = RefIcon;