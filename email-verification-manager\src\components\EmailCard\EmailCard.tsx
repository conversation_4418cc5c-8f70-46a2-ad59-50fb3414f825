import React, { useState } from 'react';
import { Card, Typography, Tag, Button, Space, message } from 'antd';
import { CopyOutlined, MailOutlined, ClockCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { EmailWithAccount } from '../../services/types';

const { Title, Text, Paragraph } = Typography;

interface EmailCardProps {
  email: EmailWithAccount;
}

const EmailCard: React.FC<EmailCardProps> = ({ email }) => {
  const [expanded, setExpanded] = useState(false);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success('已复制到剪贴板');
    } catch (error) {
      message.error('复制失败');
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
    } catch {
      return dateString;
    }
  };

  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Card
      style={{ marginBottom: 16 }}
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <MailOutlined style={{ marginRight: 8 }} />
            <Text strong>{email.subject || '(无主题)'}</Text>
          </div>
          <Tag color="blue">{email.account_name}</Tag>
        </div>
      }
      extra={
        <Space>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <ClockCircleOutlined style={{ marginRight: 4 }} />
            {formatDate(email.received_at)}
          </Text>
        </Space>
      }
    >
      <div style={{ marginBottom: 12 }}>
        <Text type="secondary">发件人：</Text>
        <Text>{email.sender}</Text>
      </div>

      {email.verification_code && (
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>
            <Text strong>验证码：</Text>
          </div>
          <div className="verification-code-highlight">
            <Space>
              <Text 
                style={{ 
                  fontSize: '18px', 
                  fontFamily: 'monospace', 
                  fontWeight: 'bold',
                  color: '#d4380d'
                }}
              >
                {email.verification_code}
              </Text>
              <Button
                type="link"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(email.verification_code!)}
              >
                复制
              </Button>
            </Space>
          </div>
        </div>
      )}

      <div>
        <div style={{ marginBottom: 8 }}>
          <Text strong>邮件内容：</Text>
          <Button
            type="link"
            size="small"
            onClick={() => setExpanded(!expanded)}
            style={{ padding: 0, marginLeft: 8 }}
          >
            {expanded ? '收起' : '展开'}
          </Button>
        </div>
        <Paragraph
          style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '4px',
            margin: 0,
            whiteSpace: 'pre-wrap',
            fontSize: '13px',
            lineHeight: '1.5',
          }}
        >
          {expanded ? email.body : truncateText(email.body)}
        </Paragraph>
      </div>
    </Card>
  );
};

export default EmailCard;
