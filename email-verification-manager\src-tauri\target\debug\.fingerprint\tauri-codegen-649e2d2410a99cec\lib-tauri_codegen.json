{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 6723982707345001717, "deps": [[3060637413840920116, "proc_macro2", false, 3511945678907558886], [4899080583175475170, "semver", false, 7200903675442271278], [7392050791754369441, "ico", false, 6850990519148872372], [8008191657135824715, "thiserror", false, 12830748378722880387], [8292277814562636972, "tauri_utils", false, 14122193339029557105], [8319709847752024821, "uuid", false, 16862441499044953711], [9451456094439810778, "regex", false, 2449279224123877531], [9689903380558560274, "serde", false, 12644071661126814469], [9857275760291862238, "sha2", false, 4054353303462043538], [10301936376833819828, "json_patch", false, 1128583256561629338], [12687914511023397207, "png", false, 10619326961501469667], [14132538657330703225, "brotli", false, 11841727477002726077], [15622660310229662834, "walkdir", false, 1473663514814095899], [16362055519698394275, "serde_json", false, 17541206816782419939], [17990358020177143287, "quote", false, 12892848900342196043], [18066890886671768183, "base64", false, 16074488397521669066]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-649e2d2410a99cec\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}