.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.content-wrapper {
  flex: 1;
  padding: 0 24px 24px;
  overflow-y: auto;
}

.verification-code-highlight {
  background: #fff2e8;
  border: 1px solid #ffbb96;
  border-radius: 4px;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: bold;
  color: #d4380d;
  text-align: center;
  margin: 8px 0;
}
