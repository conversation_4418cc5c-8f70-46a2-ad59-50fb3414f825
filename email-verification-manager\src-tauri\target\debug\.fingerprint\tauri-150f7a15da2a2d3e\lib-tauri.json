{"rustc": 1842507548689473721, "features": "[\"compression\", \"default\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"objc-exception\", \"open\", \"path-all\", \"protocol-asset\", \"regex\", \"shell-open\", \"shell-open-api\", \"tauri-runtime-wry\", \"window-close\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 12459320948781767576, "deps": [[40386456601120721, "percent_encoding", false, 4716183187666870099], [1260461579271933187, "serialize_to_javascript", false, 5907604560145896632], [1441306149310335789, "tempfile", false, 2076440754936867353], [3150220818285335163, "url", false, 6406480644215810995], [3722963349756955755, "once_cell", false, 10119690688052558844], [3988549704697787137, "open", false, 6321201780598559956], [4381063397040571828, "webview2_com", false, 8020530905907141150], [4405182208873388884, "http", false, 11575052487955420745], [4450062412064442726, "dirs_next", false, 4754508734613253962], [4899080583175475170, "semver", false, 5780744125036643049], [5180608563399064494, "tauri_macros", false, 10010940468680937073], [5610773616282026064, "build_script_build", false, 16136484350172806989], [5986029879202738730, "log", false, 2453399834231293545], [7653476968652377684, "windows", false, 1900602856807682700], [8008191657135824715, "thiserror", false, 12830748378722880387], [8292277814562636972, "tauri_utils", false, 16255483686337415295], [8319709847752024821, "uuid", false, 6688255699529898215], [9451456094439810778, "regex", false, 10941516236773083489], [9623796893764309825, "ignore", false, 5645761068230901354], [9689903380558560274, "serde", false, 12644071661126814469], [9920160576179037441, "getrandom", false, 12032847789235881657], [10629569228670356391, "futures_util", false, 13170970476968110228], [11601763207901161556, "tar", false, 16433663372059341080], [11693073011723388840, "raw_window_handle", false, 810186779276519523], [11989259058781683633, "dunce", false, 1783921420998980814], [12986574360607194341, "serde_repr", false, 15541400813964879055], [13208667028893622512, "rand", false, 13022999967511982451], [13625485746686963219, "anyhow", false, 11331521509603507155], [14162324460024849578, "tauri_runtime", false, 14407933444267013667], [14564311161534545801, "encoding_rs", false, 14640617770736607237], [16228250612241359704, "tauri_runtime_wry", false, 15909265223540138024], [16362055519698394275, "serde_json", false, 3264631440749833641], [17155886227862585100, "glob", false, 9366574394984496925], [17278893514130263345, "state", false, 14898683060407324467], [17531218394775549125, "tokio", false, 11527827444229834036], [17772299992546037086, "flate2", false, 14320801411175798052]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-150f7a15da2a2d3e\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}