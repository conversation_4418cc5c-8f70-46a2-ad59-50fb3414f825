{"rustc": 1842507548689473721, "features": "[\"array-sizes-33-128\"]", "declared_features": "[\"array-sizes-129-255\", \"array-sizes-33-128\", \"default\", \"serde\", \"std\", \"unstable-const-fn\"]", "target": 10123127388291370278, "profile": 15657897354478470176, "path": 16175327157437861468, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\arrayvec-8d2dfeeee43adaac\\dep-lib-arrayvec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}