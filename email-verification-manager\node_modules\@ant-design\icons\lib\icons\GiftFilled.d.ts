import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![gift](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2MCA4OTRjMCAxNy43IDE0LjMgMzIgMzIgMzJoMjg2VjU1MEgxNjB2MzQ0em0zODYgMzJoMjg2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU1MEg1NDZ2Mzc2em0zMzQtNjE2SDczMi40YzEzLjYtMjEuNCAyMS42LTQ2LjggMjEuNi03NCAwLTc2LjEtNjEuOS0xMzgtMTM4LTEzOC00MS40IDAtNzguNyAxOC40LTEwNCA0Ny40LTI1LjMtMjktNjIuNi00Ny40LTEwNC00Ny40LTc2LjEgMC0xMzggNjEuOS0xMzggMTM4IDAgMjcuMiA3LjkgNTIuNiAyMS42IDc0SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTQwaDM2NlYzMTBoNjh2MTcyaDM2NlYzNDJjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00MDItNGgtNzBjLTM4LjYgMC03MC0zMS40LTcwLTcwczMxLjQtNzAgNzAtNzAgNzAgMzEuNCA3MCA3MHY3MHptMTM4IDBoLTcwdi03MGMwLTM4LjYgMzEuNC03MCA3MC03MHM3MCAzMS40IDcwIDcwLTMxLjQgNzAtNzAgNzB6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
