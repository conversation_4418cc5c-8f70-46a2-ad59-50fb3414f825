"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DownSquareTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DownSquareTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DownSquareTwoTone = function DownSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DownSquareTwoTone.default
  }));
};

/**![down-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTUwLTQ0MGg0Ni45YzEwLjMgMCAxOS45IDQuOSAyNS45IDEzLjJMNTEyIDU1OC42bDEwNS4yLTE0NS40YzYtOC4zIDE1LjctMTMuMiAyNS45LTEzLjJINjkwYzYuNSAwIDEwLjMgNy40IDYuNCAxMi43bC0xNzggMjQ2YTcuOTUgNy45NSAwIDAxLTEyLjkgMGwtMTc4LTI0NmMtMy44LTUuMyAwLTEyLjcgNi41LTEyLjd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MDUuNSA2NTguN2MzLjIgNC40IDkuNyA0LjQgMTIuOSAwbDE3OC0yNDZjMy45LTUuMy4xLTEyLjctNi40LTEyLjdoLTQ2LjljLTEwLjIgMC0xOS45IDQuOS0yNS45IDEzLjJMNTEyIDU1OC42IDQwNi44IDQxMy4yYy02LTguMy0xNS42LTEzLjItMjUuOS0xMy4ySDMzNGMtNi41IDAtMTAuMyA3LjQtNi41IDEyLjdsMTc4IDI0NnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DownSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DownSquareTwoTone';
}
var _default = exports.default = RefIcon;