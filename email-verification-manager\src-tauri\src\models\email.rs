use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Email {
    pub id: i64,
    pub account_id: i64,
    pub subject: String,
    pub sender: String,
    pub body: String,
    pub verification_code: Option<String>,
    pub received_at: String,
    pub message_id: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EmailWithAccount {
    pub id: i64,
    pub account_id: i64,
    pub subject: String,
    pub sender: String,
    pub body: String,
    pub verification_code: Option<String>,
    pub received_at: String,
    pub message_id: String,
    pub account_name: String,
    pub account_email: String,
}

impl Email {
    pub fn new(
        account_id: i64,
        subject: String,
        sender: String,
        body: String,
        message_id: String,
        received_at: String,
    ) -> Self {
        Self {
            id: 0, // 将由数据库自动生成
            account_id,
            subject,
            sender,
            body,
            verification_code: None,
            received_at,
            message_id,
        }
    }

    pub fn extract_verification_code(&mut self) {
        self.verification_code = extract_code_from_text(&self.body)
            .or_else(|| extract_code_from_text(&self.subject));
    }
}

/// 从文本中提取验证码
/// 支持多种格式：6位数字、4-8位字母数字组合等
fn extract_code_from_text(text: &str) -> Option<String> {
    use regex::Regex;
    
    // 常见的验证码模式
    let patterns = vec![
        r"\b\d{6}\b",                    // 6位数字
        r"\b\d{4}\b",                    // 4位数字
        r"\b[A-Z0-9]{6}\b",             // 6位字母数字组合
        r"\b[A-Z0-9]{4,8}\b",           // 4-8位字母数字组合
        r"验证码[：:\s]*([A-Z0-9]{4,8})", // 中文验证码标识
        r"verification code[：:\s]*([A-Z0-9]{4,8})", // 英文验证码标识
        r"code[：:\s]*([A-Z0-9]{4,8})",  // 简化英文标识
    ];

    for pattern in patterns {
        if let Ok(re) = Regex::new(pattern) {
            if let Some(captures) = re.captures(text) {
                // 如果有捕获组，使用第一个捕获组，否则使用整个匹配
                let code = if captures.len() > 1 {
                    captures.get(1)?.as_str()
                } else {
                    captures.get(0)?.as_str()
                };
                
                // 验证提取的代码是否合理
                if is_valid_verification_code(code) {
                    return Some(code.to_string());
                }
            }
        }
    }
    
    None
}

/// 验证提取的验证码是否合理
fn is_valid_verification_code(code: &str) -> bool {
    let len = code.len();
    
    // 长度检查：4-8位
    if len < 4 || len > 8 {
        return false;
    }
    
    // 不能全是相同字符
    if code.chars().all(|c| c == code.chars().next().unwrap()) {
        return false;
    }
    
    // 不能是常见的无效模式
    let invalid_patterns = vec!["0000", "1111", "2222", "3333", "4444", "5555", "6666", "7777", "8888", "9999"];
    if invalid_patterns.contains(&code) {
        return false;
    }
    
    true
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_verification_code() {
        let test_cases = vec![
            ("您的验证码是：123456", Some("123456".to_string())),
            ("verification code: ABC123", Some("ABC123".to_string())),
            ("Your code is 789012", Some("789012".to_string())),
            ("验证码 456789 请在5分钟内使用", Some("456789".to_string())),
            ("no code here", None),
            ("验证码：0000", None), // 无效的全零码
        ];

        for (input, expected) in test_cases {
            assert_eq!(extract_code_from_text(input), expected, "Failed for input: {}", input);
        }
    }
}
