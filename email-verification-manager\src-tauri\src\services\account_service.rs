use crate::database::get_db_pool;
use crate::models::{Account, CreateAccountRequest, UpdateAccountRequest};
use crate::services::CryptoService;
use anyhow::Result;
use sqlx::Row;

pub struct AccountService;

impl AccountService {
    pub async fn create_account(req: CreateAccountRequest) -> Result<Account> {
        let pool = get_db_pool();
        let crypto = CryptoService::get();
        
        // 加密密码
        let encrypted_password = crypto.encrypt(&req.password)?;
        
        // 创建账号对象
        let account = Account::new(req, encrypted_password);
        
        // 插入数据库
        let result = sqlx::query(
            r#"
            INSERT INTO accounts (name, email, imap_server, imap_port, username, password, use_tls, created_at, updated_at, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&account.name)
        .bind(&account.email)
        .bind(&account.imap_server)
        .bind(account.imap_port)
        .bind(&account.username)
        .bind(&account.password)
        .bind(account.use_tls)
        .bind(&account.created_at)
        .bind(&account.updated_at)
        .bind(account.is_active)
        .execute(pool)
        .await?;

        // 获取插入的ID并返回完整的账号信息
        let id = result.last_insert_rowid();
        Self::get_account_by_id(id).await
    }

    pub async fn get_all_accounts() -> Result<Vec<Account>> {
        let pool = get_db_pool();
        
        let accounts = sqlx::query_as::<_, Account>(
            "SELECT * FROM accounts ORDER BY created_at DESC"
        )
        .fetch_all(pool)
        .await?;

        Ok(accounts)
    }

    pub async fn get_account_by_id(id: i64) -> Result<Account> {
        let pool = get_db_pool();
        
        let account = sqlx::query_as::<_, Account>(
            "SELECT * FROM accounts WHERE id = ?"
        )
        .bind(id)
        .fetch_one(pool)
        .await?;

        Ok(account)
    }

    pub async fn get_active_accounts() -> Result<Vec<Account>> {
        let pool = get_db_pool();
        
        let accounts = sqlx::query_as::<_, Account>(
            "SELECT * FROM accounts WHERE is_active = TRUE ORDER BY created_at DESC"
        )
        .fetch_all(pool)
        .await?;

        Ok(accounts)
    }

    pub async fn update_account(id: i64, req: UpdateAccountRequest) -> Result<Account> {
        let pool = get_db_pool();
        let crypto = CryptoService::get();
        
        // 获取现有账号
        let mut account = Self::get_account_by_id(id).await?;
        
        // 如果需要更新密码，先加密
        let encrypted_password = if req.password.is_some() {
            Some(crypto.encrypt(req.password.as_ref().unwrap())?)
        } else {
            None
        };
        
        // 更新账号信息
        account.update(req, encrypted_password);
        
        // 更新数据库
        sqlx::query(
            r#"
            UPDATE accounts 
            SET name = ?, email = ?, imap_server = ?, imap_port = ?, username = ?, password = ?, 
                use_tls = ?, updated_at = ?, is_active = ?
            WHERE id = ?
            "#,
        )
        .bind(&account.name)
        .bind(&account.email)
        .bind(&account.imap_server)
        .bind(account.imap_port)
        .bind(&account.username)
        .bind(&account.password)
        .bind(account.use_tls)
        .bind(&account.updated_at)
        .bind(account.is_active)
        .bind(id)
        .execute(pool)
        .await?;

        Ok(account)
    }

    pub async fn delete_account(id: i64) -> Result<()> {
        let pool = get_db_pool();
        
        sqlx::query("DELETE FROM accounts WHERE id = ?")
            .bind(id)
            .execute(pool)
            .await?;

        Ok(())
    }

    pub async fn get_decrypted_password(account: &Account) -> Result<String> {
        let crypto = CryptoService::get();
        crypto.decrypt(&account.password)
    }
}
