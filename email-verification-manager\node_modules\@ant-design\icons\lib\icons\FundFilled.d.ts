import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![fund](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNiAxNjRIOTRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tOTIuMyAxOTQuNGwtMjk3IDI5Ny4yYTguMDMgOC4wMyAwIDAxLTExLjMgMEw0MTAuOSA1NDEuMSAyMzguNCA3MTMuN2E4LjAzIDguMDMgMCAwMS0xMS4zIDBsLTM2LjgtMzYuOGE4LjAzIDguMDMgMCAwMTAtMTEuM2wyMTQuOS0yMTVjMy4xLTMuMSA4LjItMy4xIDExLjMgMEw1MzEgNTY1bDI1NC41LTI1NC42YzMuMS0zLjEgOC4yLTMuMSAxMS4zIDBsMzYuOCAzNi44YzMuMiAzIDMuMiA4LjEuMSAxMS4yeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
