import { useState, useCallback } from 'react';
import { accountApi, handleApiError } from '../services/tauri-api';
import type { Account, CreateAccountRequest, UpdateAccountRequest } from '../services/types';

export const useAccounts = () => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshAccounts = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await accountApi.getAllAccounts();
      setAccounts(result);
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Failed to fetch accounts:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const addAccount = useCallback(async (accountData: CreateAccountRequest): Promise<Account> => {
    setLoading(true);
    setError(null);
    try {
      const newAccount = await accountApi.createAccount(accountData);
      setAccounts(prev => [newAccount, ...prev]);
      return newAccount;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAccount = useCallback(async (id: number, accountData: UpdateAccountRequest): Promise<Account> => {
    setLoading(true);
    setError(null);
    try {
      const updatedAccount = await accountApi.updateAccount(id, accountData);
      setAccounts(prev => prev.map(account => 
        account.id === id ? updatedAccount : account
      ));
      return updatedAccount;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteAccount = useCallback(async (id: number): Promise<void> => {
    setLoading(true);
    setError(null);
    try {
      await accountApi.deleteAccount(id);
      setAccounts(prev => prev.filter(account => account.id !== id));
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const testConnection = useCallback(async (accountData: CreateAccountRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      const result = await accountApi.testConnection(accountData);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    accounts,
    loading,
    error,
    clearError,
    refreshAccounts,
    addAccount,
    updateAccount,
    deleteAccount,
    testConnection,
  };
};
