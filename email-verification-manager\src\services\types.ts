// 账号相关类型定义
export interface Account {
  id: number;
  name: string;
  email: string;
  imap_server: string;
  imap_port: number;
  username: string;
  password: string;
  use_tls: boolean;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface CreateAccountRequest {
  name: string;
  email: string;
  imap_server: string;
  imap_port: number;
  username: string;
  password: string;
  use_tls: boolean;
  is_active: boolean;
}

export interface UpdateAccountRequest {
  name?: string;
  email?: string;
  imap_server?: string;
  imap_port?: number;
  username?: string;
  password?: string;
  use_tls?: boolean;
  is_active?: boolean;
}

// 邮件相关类型定义
export interface Email {
  id: number;
  account_id: number;
  subject: string;
  sender: string;
  body: string;
  verification_code?: string;
  received_at: string;
  message_id: string;
}

export interface EmailWithAccount extends Email {
  account_name: string;
  account_email: string;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 邮箱服务器预设配置
export interface EmailProvider {
  name: string;
  imap_server: string;
  imap_port: number;
  use_tls: boolean;
}

// 常用邮箱服务器配置
export const EMAIL_PROVIDERS: EmailProvider[] = [
  {
    name: 'Gmail',
    imap_server: 'imap.gmail.com',
    imap_port: 993,
    use_tls: true,
  },
  {
    name: 'Outlook/Hotmail',
    imap_server: 'outlook.office365.com',
    imap_port: 993,
    use_tls: true,
  },
  {
    name: 'QQ邮箱',
    imap_server: 'imap.qq.com',
    imap_port: 993,
    use_tls: true,
  },
  {
    name: '163邮箱',
    imap_server: 'imap.163.com',
    imap_port: 993,
    use_tls: true,
  },
  {
    name: '126邮箱',
    imap_server: 'imap.126.com',
    imap_port: 993,
    use_tls: true,
  },
  {
    name: 'Yahoo',
    imap_server: 'imap.mail.yahoo.com',
    imap_port: 993,
    use_tls: true,
  },
];
