"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _LaptopOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/LaptopOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var LaptopOutlined = function LaptopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _LaptopOutlined.default
  }));
};

/**![laptop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1Ni45IDg0NS4xTDg5Ni40IDYzMlYxNjhjMC0xNy43LTE0LjMtMzItMzItMzJoLTcwNGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDY0TDY3LjkgODQ1LjFDNjAuNCA4NjYgNzUuOCA4ODggOTggODg4aDgyOC44YzIyLjIgMCAzNy42LTIyIDMwLjEtNDIuOXpNMjAwLjQgMjA4aDYyNHYzOTVoLTYyNFYyMDh6bTIyOC4zIDYwOGw4LjEtMzdoMTUwLjNsOC4xIDM3SDQyOC43em0yMjQgMGwtMTkuMS04Ni43Yy0uOC0zLjctNC4xLTYuMy03LjgtNi4zSDM5OC4yYy0zLjggMC03IDIuNi03LjggNi4zTDM3MS4zIDgxNkgxNTFsNDIuMy0xNDloNjM4LjJsNDIuMyAxNDlINjUyLjd6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(LaptopOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LaptopOutlined';
}
var _default = exports.default = RefIcon;